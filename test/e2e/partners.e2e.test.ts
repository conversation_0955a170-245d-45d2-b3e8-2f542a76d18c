import supertest from 'supertest'
import { app } from '@modules/partnersApi/server'
import { SpaceType } from '@modules/communities/types/spaceType'
import {
  createCommunityInitialData,
  createInitialData
} from '@test/helpers/partnersApi.helper'
import { createCommunity, createSpace } from '@test/helpers/community.helper'
import { ObjectId } from '@modules/communities/types/id'
import { createExternalLink } from '@test/helpers/externalLink.helper'
import { handler } from '@modules/partnersApi/services/authorizer'
import { createOrganization } from '@test/helpers/organization.helper'
import { customNanoId } from '../../src/core/util'
import { ExternalLinkCollection } from '@modules/communities/types/externalLink'

describe('Partners API', () => {
  describe('authorizer function', () => {
    it('Should authorize a valid API key', async () => {
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 365)

      const apiKeyId = customNanoId(8)
      const organization = await createOrganization({
        apiKeys: [
          {
            _id: apiKeyId,
            name: 'test',
            isActive: true,
            hash: 'a161029d0bbb209c27502f942248acc134d0cd171dab5c3aa476dc24f0b98c9337eee11e9a3cad8e5fce1d1b37cc3d6ce1089b3607a901ee79305fddb7459bb9',
            expiresAt: expiresAt,
            createdAt: new Date()
          }
        ]
      })
      const event = {
        version: '2.0',
        type: 'REQUEST',
        routeArn:
          'arn:aws:execute-api:us-east-1:112470036249:hlc0vgka30/$default/GET/v3/communities/6391db5526846814a41247ce/spaces',
        identitySource: [`Bearer pp_123-456-${apiKeyId}-789`],
        routeKey: 'ANY /v3/{proxy+}',
        rawPath: '/v3/communities/6391db5526846814a41247ce/spaces',
        rawQueryString: ''
      }

      const response = await handler(event)

      expect(response.isAuthorized).toBe(true)
      expect(response.context.organizationId).toEqual(
        organization._id.toString()
      )
    })

    it.each([
      {
        _id: customNanoId(8),
        name: 'test',
        isActive: false,
        hash: 'a161029d0bbb209c27502f942248acc134d0cd171dab5c3aa476dc24f0b98c9337eee11e9a3cad8e5fce1d1b37cc3d6ce1089b3607a901ee79305fddb7459bb9',
        expiresAt: new Date(new Date().setDate(new Date().getDate() + 365)),
        createdAt: new Date()
      },
      {
        _id: customNanoId(8),
        name: 'test',
        isActive: true,
        hash: 'wrong-hash',
        expiresAt: new Date(new Date().setDate(new Date().getDate() + 365)),
        createdAt: new Date()
      },
      {
        _id: customNanoId(8),
        name: 'test',
        isActive: true,
        hash: 'a161029d0bbb209c27502f942248acc134d0cd171dab5c3aa476dc24f0b98c9337eee11e9a3cad8e5fce1d1b37cc3d6ce1089b3607a901ee79305fddb7459bb9',
        expiresAt: new Date(new Date().setDate(new Date().getDate() - 1)),
        createdAt: new Date()
      }
    ])('Should not authorize an invalid API key', async (params) => {
      await createOrganization({
        apiKeys: [params]
      })
      const event = {
        version: '2.0',
        type: 'REQUEST',
        routeArn:
          'arn:aws:execute-api:us-east-1:112470036249:hlc0vgka30/$default/GET/v3/communities/6391db5526846814a41247ce/spaces',
        identitySource: [`Bearer pp_123-456-${params._id}-789`],
        routeKey: 'ANY /v3/{proxy+}',
        rawPath: '/v3/communities/6391db5526846814a41247ce/spaces',
        rawQueryString: ''
      }

      const response = await handler(event)
      expect(response.isAuthorized).toBe(false)
    })
  })

  describe('/communities/:id/spaces', () => {
    it('Should successfully retrieve a list of spaces', async () => {
      const { community, space, externalLink } = await createInitialData()
      const response = await supertest(app)
        .get(`/v3/communities/${community._id.toString()}/spaces`)
        .set({
          'content-type': 'application/json',
          organizationId: community.organization._id.toString()
        })

      expect(response.status).toBe(200)
      expect(response.body.data.length).toBe(1)
      const resSpace = response.body.data[0]

      const virtualTour = `${process.env.WEB_VIEWER_BASE_URL}?token=${space.token}`
      expect(resSpace.id).toBe(space._id.toString())
      expect(resSpace.virtualTourUrl).toBe(virtualTour)
      expect(resSpace.unitName).toBe(space.unit)
      expect(resSpace.address).toEqual(space.building.address)
      expect(resSpace.externalIds).toBeInstanceOf(Array)
      expect(resSpace.externalIds.length).toBe(1)
      expect(resSpace.externalIds[0].id).toBe(externalLink.externalId)
      expect(resSpace.externalIds[0].service).toBe(
        externalLink.service.toString()
      )
      if (resSpace.type === SpaceType.Unit) {
        const layout = space.bedrooms + 'br/' + space.bathrooms + 'ba'
        expect(resSpace.unitDetails.layout).toBe(layout)
        expect(resSpace.unitDetails.isModelUnit).toBe(space.isModelUnit)
        expect(resSpace.unitDetails.layoutType).toBeDefined()
      }
    })

    it('Should return layoutType from floorPlan.name when available', async () => {
      const community = await createCommunity()
      await createSpace({
        community,
        type: SpaceType.Unit,
        bedrooms: 2,
        bathrooms: 1,
        floorPlan: {
          name: 'Studio Deluxe',
          externalId: 'studio-deluxe-001'
        }
      })

      const response = await supertest(app)
        .get(`/v3/communities/${community._id.toString()}/spaces`)
        .set({
          'content-type': 'application/json',
          organizationId: community.organization._id.toString()
        })

      expect(response.status).toBe(200)
      expect(response.body.data.length).toBe(1)
      const resSpace = response.body.data[0]

      expect(resSpace.unitDetails.layoutType).toBe('Studio Deluxe')
    })

    it('Should successfully retrieve a list of spaces filtering by space type', async () => {
      const community = await createCommunity()
      await Promise.all([
        createSpace({
          community,
          type: SpaceType.Unit
        }),
        createSpace({
          community,
          type: SpaceType.Amenity
        })
      ])

      const [response0, response1, response2] = await Promise.all([
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          }),
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .query({ type: SpaceType.Unit })
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          }),
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .query({ type: SpaceType.Amenity })
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          })
      ])
      expect(response0.status).toBe(200)
      expect(response0.body.data.length).toBe(2)

      expect(response1.status).toBe(200)
      expect(response1.body.data.length).toBe(1)
      expect(response1.body.data[0].type).toBe(SpaceType.Unit)

      expect(response2.status).toBe(200)
      expect(response2.body.data.length).toBe(1)
      expect(response2.body.data[0].type).toBe(SpaceType.Amenity)
    })

    it('Should successfully retrieve a list of spaces filtering by space availability', async () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      const community = await createCommunity()
      const [space0, space1] = await Promise.all([
        createSpace({
          community,
          type: SpaceType.Unit,
          availableDate: yesterday,
          unit: 'Unit 0'
        }),
        createSpace({
          community,
          type: SpaceType.Unit,
          availableDate: tomorrow,
          unit: 'Unit 1'
        })
      ])

      const [response0, response1, response2] = await Promise.all([
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          }),
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .query({ availability: 'available' })
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          }),
        supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .query({ availability: 'unavailable' })
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          })
      ])
      expect(response0.status).toBe(200)
      expect(response0.body.data.length).toBe(2)

      expect(response1.status).toBe(200)
      expect(response1.body.data.length).toBe(1)
      expect(response1.body.data[0].unitName).toBe(space0.unit)

      expect(response2.status).toBe(200)
      expect(response2.body.data.length).toBe(1)
      expect(response2.body.data[0].unitName).toBe(space1.unit)
    })
    it('Should fail to retrieve a list of spaces when community does not exist', async () => {
      const response = await supertest(app)
        .get(`/v3/communities/${new ObjectId()}/spaces`)
        .set({
          'content-type': 'application/json',
          organizationId: new ObjectId().toString()
        })
      expect(response.status).toBe(404)
      expect(response.body.message).toBe('Community or spaces not found.')
    })
    it.each([
      {
        type: 'invalidType'
      },
      {
        availability: 'invalidAvailability'
      }
    ])(
      'Should fail to retrieve a list of spaces when query is invalid',
      async (query) => {
        const community = await createCommunity()
        const response = await supertest(app)
          .get(`/v3/communities/${community._id.toString()}/spaces`)
          .query(query)
          .set({
            'content-type': 'application/json',
            organizationId: community.organization._id.toString()
          })
        expect(response.status).toBe(400)
        expect(response.body.message).toBe('Invalid request query')
      }
    )
    it('Should successfully retrieve a list of spaces filtering by external service name and id', async () => {
      const { community, space, externalLink } =
        await createCommunityInitialData()
      const spaceExternalLink = await createExternalLink({
        objectId: space._id.toString(),
        externalId: 'space-1',
        collectionPath: ExternalLinkCollection.Spaces
      })
      const response = await supertest(app)
        .get(`/v3/communities/${externalLink.externalId}/spaces`)
        .query({ service: externalLink.service })
        .set({
          'content-type': 'application/json',
          organizationId: community.organization._id.toString()
        })
      expect(response.status).toBe(200)
      expect(response.body.data.length).toBe(1)
      const resSpace = response.body.data[0]

      const virtualTour = `${process.env.WEB_VIEWER_BASE_URL}?token=${space.token}`
      expect(resSpace.id).toBe(space._id.toString())
      expect(resSpace.virtualTourUrl).toBe(virtualTour)
      expect(resSpace.unitName).toBe(space.unit)
      expect(resSpace.address).toEqual(space.building.address)
      expect(resSpace.externalIds).toBeInstanceOf(Array)
      expect(resSpace.externalIds.length).toBe(1)
      expect(resSpace.externalIds[0].id).toBe(spaceExternalLink.externalId)
      expect(resSpace.externalIds[0].service).toBe(
        spaceExternalLink.service.toString()
      )
      if (resSpace.type === SpaceType.Unit) {
        const layout = space.bedrooms + 'br/' + space.bathrooms + 'ba'
        expect(resSpace.unitDetails.layout).toBe(layout)
        expect(resSpace.unitDetails.isModelUnit).toBe(space.isModelUnit)
      }
    })
  })

  describe('/spaces/:id/virtual-tour-url', () => {
    it('Should return a space link using a peek id', async () => {
      const { space } = await createInitialData()

      const response = await supertest(app)
        .get(`/v3/spaces/${space._id.toString()}/virtual-tour-url`)
        .set({
          'content-type': 'application/json',
          organizationId: space.community.organization._id.toString()
        })
      expect(response.status).toBe(200)

      const link = `${process.env.WEB_VIEWER_BASE_URL}?token=${space.token}`

      expect(response.body.data.virtualTourUrl).toBe(link)
      expect(response.body.data.id).toBe(space._id.toString())
      expect(response.body.data.isTracked).toBe(false)
    })

    it('Should fail to return a space link if the space is not found', async () => {
      const response = await supertest(app)
        .get(`/v3/spaces/${new ObjectId()}/virtual-tour-url`)
        .set({
          'content-type': 'application/json',
          organizationId: new ObjectId().toString()
        })
      expect(response.status).toBe(404)
      expect(response.body.message).toBe('Space not found.')
    })

    it('Should return a space link using a vendor id', async () => {
      const { space, externalLink } = await createInitialData()

      const response = await supertest(app)
        .get(`/v3/spaces/${externalLink.externalId}/virtual-tour-url`)
        .query({
          service: externalLink.service
        })
        .set({
          'content-type': 'application/json',
          organizationId: space.community.organization._id.toString()
        })
      expect(response.status).toBe(200)

      const link = `${process.env.WEB_VIEWER_BASE_URL}?token=${space.token}`

      expect(response.body.data.virtualTourUrl).toBe(link)
      expect(response.body.data.id).toBe(space._id.toString())
      expect(response.body.data.isTracked).toBe(false)
    })

    it('Should fail to return a space link if the space is not found using a vendor id', async () => {
      const response = await supertest(app)
        .get(`/v3/spaces/${new ObjectId()}/virtual-tour-url`)
        .query({
          service: 'entrata'
        })
        .set({
          'content-type': 'application/json',
          organizationId: new ObjectId().toString()
        })
      expect(response.status).toBe(404)
      expect(response.body.message).toBe('Space not found.')
    })

    it.each([
      { email: '<EMAIL>', phone: '1231231234' },
      { email: '<EMAIL>' },
      { phone: '1231231234' }
    ])('Should return a space link and create a prospect', async (params) => {
      const { space } = await createInitialData()

      const response = await supertest(app)
        .get(`/v3/spaces/${space._id.toString()}/virtual-tour-url`)
        .query({
          ...params
        })
        .set({
          'content-type': 'application/json',
          organizationId: space.community.organization._id.toString()
        })

      expect(response.status).toBe(200)

      const link = `${process.env.WEB_VIEWER_BASE_URL}?token=${space.token}`

      expect(response.body.data.virtualTourUrl).toBe(link)
      expect(response.body.data.isTracked).toBe(true)
    })
    it.each([
      {
        email: 'invalidEmail.com@peek',
        phone: '1231231234'
      },
      {
        email: '<EMAIL>',
        phone: '1231231234',
        service: 'invalid service'
      }
    ])(
      'Should fail to return a space link if prospect email is invalid',
      async (params) => {
        const { space } = await createInitialData()

        const response = await supertest(app)
          .get(`/v3/spaces/${space._id.toString()}/virtual-tour-url`)
          .query({
            ...params
          })
          .set({
            'content-type': 'application/json',
            organizationId: space.community.organization._id.toString()
          })

        expect(response.status).toBe(400)
        expect(response.body.message).toBe('Invalid request query')
      }
    )
  })
})
