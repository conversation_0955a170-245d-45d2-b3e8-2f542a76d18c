import { Id, ObjectId } from '@modules/communities/types/id'
import { Node } from '@modules/communities/types/node'
import { Infospot } from '@modules/communities/types/infospot'
import { NodeLink, NodeLinkType } from '@modules/communities/types/nodeLink'
import mongoose from 'mongoose'
import {
  Community,
  CommunityPlan,
  CommunityTours,
  GeoPlace,
  planFeatures
} from '@modules/communities/types/community'
import { faker } from '@faker-js/faker'
import { Space } from '@modules/communities/types/space'
import { Building } from '@modules/communities/types/building'
import { SpaceType } from '@modules/communities/types/spaceType'
import { CommunityModel } from '@modules/communities/models/community'
import { Address } from '@modules/communities/types/address'
import { SpaceModel } from '@modules/communities/models/space'
import { BuildingModel } from '@modules/communities/models/building'
import { OrganizationModel } from '@modules/users/models/organization'

export const mockNodeLabels = [
  'ENTRYWAY',
  'BATHROOM',
  'HALL',
  'KITCHEN',
  'SWIMMING POOL',
  'GAMING ROOM',
  'PARTY ROOM',
  'DANCING ROOM'
]

export const dropTransformTestCommunities = async () => {
  const communitiesToDelete = (await CommunityModel.find({
    description: 'transform test community'
  }).lean()) as Community[]

  const ids = communitiesToDelete.map((community) => community._id)

  await BuildingModel.deleteMany({
    communityId: { $in: ids }
  })
  await SpaceModel.deleteMany({
    'community._id': { $in: ids }
  })
  await CommunityModel.deleteMany({
    _id: { $in: ids }
  })
}

export const getNodeById = (nodes: Node[], id: Id) => {
  return nodes.find((node) => node._id.toString() === id.toString())
}

export const mockNode = (params?: Partial<Node>): Node => ({
  _id: new mongoose.Types.ObjectId(),
  label: 'test',
  photo: {
    url: 'https://peek.us',
    lowResUrl: 'https://peek.us'
  },
  rotation: {
    pitch: 1,
    roll: 2,
    yaw: 3
  },
  nodeLinks: [mockNodeLink()],
  ...params
})

export const mockInfospot: Partial<Infospot> = {
  text: 'test',
  position: {
    x: 1,
    y: 2,
    z: 3
  }
}

export const mockNodeLink = (params?: Partial<NodeLink>): NodeLink => ({
  node: new mongoose.Types.ObjectId(),
  label: 'test',
  position: {
    x: 1,
    y: 2,
    z: 3
  },
  type: NodeLinkType.Node,
  rotation: {
    pitch: 0,
    yaw: 0
  },
  ...params
})

export const mockBuilding = (params?: Partial<Building>): Building => {
  return {
    _id: new mongoose.Types.ObjectId(),
    address: mockAddress(),
    alternativeName: faker.location.city(),
    communityId: new mongoose.Types.ObjectId(),
    spaceIds: [],
    ...params
  }
}

export const mockAddress = (params?: Partial<Address>): Address => {
  return {
    street: faker.location.streetAddress(),
    city: faker.location.city(),
    state: faker.location.state(),
    country: faker.location.country(),
    postalCode: faker.location.zipCode(),
    latitude: Number(faker.location.latitude()),
    longitude: Number(faker.location.longitude()),
    ...params
  }
}

const mockedBuilding = mockBuilding()

export const mockSpace = (params?: Partial<Space>): Space => {
  return {
    _id: new mongoose.Types.ObjectId(),
    type: faker.helpers.arrayElement([
      SpaceType.Unit,
      SpaceType.Amenity,
      SpaceType.Other
    ]),
    community: {
      _id: new mongoose.Types.ObjectId(),
      organization: {
        _id: new mongoose.Types.ObjectId(),
        name: faker.company.name()
      },
      name: faker.company.name(),
      logo: faker.image.url(),
      primaryColor: faker.internet.color(),
      applyText: faker.lorem.sentence(),
      applyUrl: faker.internet.url(),
      gaTrackingId: faker.helpers.fromRegExp(/([A-Za-z0-9]+(-[A-Za-z0-9]+)+)/i),
      customJs: faker.lorem.sentence(),
      secondaryColor: faker.internet.color(),
      displayPriceField: 'rentPrices.12.price'
    },
    nodes: [mockNode()],
    startNode: new mongoose.Types.ObjectId(),
    building: {
      _id: mockedBuilding._id,
      address: mockedBuilding.address
    },
    isMultiRes: faker.datatype.boolean(),
    isHorizonLevel: faker.datatype.boolean(),
    deletedAt: null,
    isComplete: faker.datatype.boolean(),
    createdBy: new mongoose.Types.ObjectId(),
    publishGmbStatus: 'unpublished',
    sgtEnabled: faker.datatype.boolean(),
    token: faker.string.uuid(),
    unit: faker.location.buildingNumber(),
    floorPlan: {
      url: faker.internet.url(),
      name: faker.lorem.word()
    },
    rentPrices: [
      {
        termInMonths: 12,
        price: faker.number.float(),
        enabled: true
      }
    ],
    displayPrice: faker.number.float(),
    address: mockAddress(),
    availableDate: faker.date.future(),
    pricesMetadata: {
      price: faker.number.float(),
      pricePerSquareFoot: faker.number.float()
    },
    tourCapturedDate: faker.date.past(),
    ...params
  } as Space
}

export const mockGeoPlace = (params?: Partial<GeoPlace>): GeoPlace => {
  return {
    latitude: faker.location.latitude(),
    longitude: faker.location.longitude(),
    name: faker.company.name(),
    drivingDuration: faker.number.int(),
    mode: faker.string.alphanumeric(),
    transitLine: [faker.string.uuid()],
    walkingDuration: faker.number.int(),
    ...params
  }
}

export const mockCommunity = (params?: Partial<Community>): Community => {
  return {
    _id: new ObjectId(),
    name: faker.company.name(),
    buildings: [mockedBuilding],
    startNode: new mongoose.Types.ObjectId(),
    pointOfInterest: {
      nearbyGrocery: mockGeoPlace(),
      nearbyGym: mockGeoPlace(),
      nearbyPark: mockGeoPlace(),
      nearbyTransit: [mockGeoPlace()]
    },
    address: mockAddress(),
    organization: {
      _id: new mongoose.Types.ObjectId(),
      name: faker.company.name()
    },
    leadsEmail: faker.internet.email(),
    isActive: true,
    description: 'some description here',
    showContactForm: true,
    canBypassContactForm: true,
    autoDisplayContactFormNavigationCount: 0,
    plan: CommunityPlan.MarketingSuite,
    assumptions: {
      estimateTourSaveRate: 0.5,
      estimateLaborRate: 28,
      schedulingOverhead: 0.25,
      avgTimeSpentOnsite: 1,
      avgQACall: 0.083,
      questionRate: 0.3
    },
    features: [planFeatures.Analytics],
    displayPriceField: 'rentPrices.12',
    sgtSupportPhoneNumber: faker.phone.number(),
    communityStyle: {
      primaryColor: faker.internet.color(),
      secondaryColor: faker.internet.color()
    },
    communityInfo: {
      logo: faker.image.url(),
      applyText: faker.lorem.sentence(),
      applyUrl: faker.internet.url(),
      gaTrackingId: faker.helpers.fromRegExp(/([A-Za-z0-9]+(-[A-Za-z0-9]+)+)/i),
      customJs: faker.lorem.sentence(),
      displayBuildingName: faker.datatype.boolean()
    },
    communityContact: {
      contactInstructions: faker.lorem.sentence(),
      contactName: faker.person.fullName(),
      contactPhone: faker.phone.number()
    },
    showAddress: faker.datatype.boolean(),
    mapLink: faker.internet.url(),
    tours: {
      [CommunityTours.AGT]: {
        isAvailable: true
      },
      [CommunityTours.SGT]: {
        isAvailable: true
      }
    },
    ...params
  }
}

export const createCommunity = async (
  params?: Partial<Community>
): Promise<Community> => {
  const community = await mockCommunity(params)
  return CommunityModel.create(community)
}

export const createSpace = async (params?: Partial<Space>): Promise<Space> => {
  const space = mockSpace(params)
  return SpaceModel.create(space)
}

export const createBuilding = async (
  params?: Partial<Building>
): Promise<Building> => {
  const building = mockBuilding(params)
  return BuildingModel.create(building)
}

export const createInitialData = async () => {
  const community = await createCommunity({
    organization: await OrganizationModel.findOne({ name: 'Organization A' })
  })

  const building = await createBuilding({
    communityId: community._id
  })

  const space = await createSpace({
    building,
    community: {
      _id: community._id,
      name: community.name,
      organization: {
        _id: community.organization._id,
        name: community.organization.name
      },
      displayPriceField: community.displayPriceField
    }
  })

  return { space, community, building }
}

export const removeSpaceById = async (spaceId: string) =>
  SpaceModel.deleteOne({ _id: spaceId })

export const removeCommunityById = async (communityId: string) =>
  CommunityModel.deleteOne({ _id: communityId })

export const removeBuildingById = async (buildingId: string) =>
  BuildingModel.deleteOne({ _id: buildingId })

export const getCommunityNodes = async (communityId: string) => {
  const spaces = (await SpaceModel.find({
    'community._id': communityId
  })
    .select('nodes community type startNode building')
    .lean()) as Space[]
  const nodes: Node[] = []
  spaces.forEach((space) => {
    nodes.push(...space.nodes)
  })
  return { nodes, spaces }
}
