import { DXSettingServices } from '@modules/communities/types/dxSetting'
import {
  Property,
  FloorPlan,
  Unit,
  LeaseTerms,
  Price
} from '@modules/cortland/types/cortlandTypes'
import { mockDXSetting } from './dxSetting.helper'
import { parse } from 'date-fns'

export const cortlandLeaseTerms: LeaseTerms = {
  new: 1,
  renewal: 2,
  transfer: 3
}

export const cortlandUnit1: Unit = {
  id: 123,
  unitNumber: '1111',
  buildingId: '123',
  buildingNumber: '123',
  floorNumber: '123',
  isAvailable: true,
  isVacant: true,
  availableOnDt: '2025-07-19T00:00:00.000Z',
  minRent: 1234,
  maxRent: 1234
}

export const cortlandUnit1Prices: Price[] = [
  {
    id: 123,
    price: 1300,
    term: 3,
    propertyId: 123,
    floorPlanId: 123,
    unitId: 123,
    unitNumber: '1111',
    buildingId: 123,
    buildingNumber: '123',
    leaseStartDt: '2021-01-01',
    leaseEndDt: '2021-01-01',
    earlyOffer: false,
    offerTerm: 0,
    generatedOnDT: '2021-01-01',
    file: '123',
    createdAt: '2021-01-01'
  },
  {
    id: 123,
    price: 1200,
    term: 4,
    propertyId: 123,
    floorPlanId: 123,
    unitId: 123,
    unitNumber: '1111',
    buildingId: 123,
    buildingNumber: '123',
    leaseStartDt: '2021-01-01',
    leaseEndDt: '2021-01-01',
    earlyOffer: false,
    offerTerm: 0,
    generatedOnDT: '2021-01-01',
    file: '123',
    createdAt: '2021-01-01'
  },
  {
    id: 123,
    price: 1100,
    term: 4,
    propertyId: 123,
    floorPlanId: 123,
    unitId: 123,
    unitNumber: '1111',
    buildingId: 123,
    buildingNumber: '123',
    leaseStartDt: '2021-01-01',
    leaseEndDt: '2021-01-01',
    earlyOffer: false,
    offerTerm: 0,
    generatedOnDT: '2021-01-01',
    file: '123',
    createdAt: '2021-01-01'
  },
  {
    id: 123,
    price: 1500,
    term: 4,
    propertyId: 123,
    floorPlanId: 123,
    unitId: 123,
    unitNumber: '1111',
    buildingId: 123,
    buildingNumber: '123',
    leaseStartDt: '2021-01-01',
    leaseEndDt: '2021-01-01',
    earlyOffer: false,
    offerTerm: 0,
    generatedOnDT: '2021-01-01',
    file: '123',
    createdAt: '2021-01-01'
  }
]

export const cortlandUnit2: Unit = {
  id: 123,
  unitNumber: '2222',
  buildingId: '123',
  buildingNumber: '123',
  floorNumber: '123',
  isAvailable: false,
  isVacant: false,
  availableOnDt: '2025-07-19T00:00:00.000Z',
  minRent: 1234,
  maxRent: 1234
}

export const cortlandUnit3: Unit = {
  id: 123,
  unitNumber: '3333',
  buildingId: '123',
  buildingNumber: '123',
  floorNumber: '123',
  isAvailable: true,
  isVacant: false,
  availableOnDt: '2021-01-01',
  minRent: 1234,
  maxRent: 1234
}

export const cortlandFloorplan: FloorPlan = {
  id: 123,
  name: 'Cortland Floorplan',
  bedrooms: 1,
  bathrooms: 1,
  sqFt: 1234,
  minRent: 1234,
  maxRent: 1234,
  units: [cortlandUnit1, cortlandUnit2, cortlandUnit3]
}

export const cortlandProperty: Property = {
  id: 123,
  name: 'Cortland Property',
  phone: '1234567890',
  email: '<EMAIL>',
  city: 'Cortland',
  state: 'NY',
  zip: '12345',
  address: '123 Main St',
  leaseTermsUpdateDT: '2021-01-01',
  propertyUpdateDT: '2021-01-01',
  availabilityUpdateDT: '2021-01-01',
  pricingUpdateDT: '2025-03-23T19:36:30.296Z',
  leaseTerms: cortlandLeaseTerms,
  floorPlans: [cortlandFloorplan]
}

export const expectedSpaceEventBody1 = {
  setting: {
    dxSettingId: '67dffc951b08caf35bc30869'
  },
  community: {
    _id: '67dffc951b08caf35bc30869'
  },
  space: {
    bathrooms: 1,
    bedrooms: 1,
    externalSpaceStatus: ['Available'],
    availabilityStatus: 'Available',
    availableStatusesMetadata: {
      isAvailable: true,
      isVacant: true
    },
    availableDatesMetadata: {
      availableOnDt: parse('2025-07-19', 'yyyy-MM-dd', new Date())
    },
    isMarketable: true,
    floorPlan: {
      name: 'Cortland Floorplan',
      externalId: '123'
    },
    unit: '1111',
    type: 'unit',
    unitSize: 1234,
    isVisible: true,
    rentPrices: [
      {
        enabled: true,
        price: 1300,
        termInMonths: 3
      },
      {
        enabled: true,
        price: 1100,
        termInMonths: 4
      }
    ],
    pricesMetadata: {
      minRent: 1234,
      maxRent: 1234
    }
  },
  address: {
    city: 'Cortland',
    state: 'NY',
    postalCode: '12345',
    street: '123 Main St'
  },
  external: {
    externalLinkService: 'cortland',
    spaces: [
      {
        externalId: '123',
        externalName: 'unit.id',
        service: 'cortland'
      }
    ]
  }
}

export const expectedSpaceEventBody2 = {
  setting: {
    dxSettingId: '67dffc951b08caf35bc30869'
  },
  community: {
    _id: '67dffc951b08caf35bc30869'
  },
  space: {
    bathrooms: 1,
    bedrooms: 1,
    externalSpaceStatus: ['Unavailable'],
    availabilityStatus: 'Unavailable',
    availableStatusesMetadata: {
      isAvailable: false,
      isVacant: false
    },
    availableDatesMetadata: {
      availableOnDt: parse('2025-07-19', 'yyyy-MM-dd', new Date())
    },
    isMarketable: false,
    floorPlan: {
      name: 'Cortland Floorplan',
      externalId: '123'
    },
    unit: '2222',
    type: 'unit',
    unitSize: 1234,
    isVisible: false,
    rentPrices: [],
    pricesMetadata: {
      minRent: 1234,
      maxRent: 1234
    }
  },
  address: {
    city: 'Cortland',
    state: 'NY',
    postalCode: '12345',
    street: '123 Main St'
  },
  external: {
    externalLinkService: 'cortland',
    spaces: [
      {
        externalId: '123',
        externalName: 'unit.id',
        service: 'cortland'
      }
    ]
  }
}

export const getMockedDXSetting = () => {
  return mockDXSetting({
    service: DXSettingServices.CORTLAND,
    dataExchange: {
      syncPms: true,
      syncProspect: false,
      syncSgt: false,
      syncIls: false,
      syncScheduleATour: false,
      syncPIData: false,
      syncLease: false,
      syncProspectStatus: false,
      syncPiSummaries: {
        store: false,
        send: false
      }
    },
    cortland: {
      propertyId: 1231234
    }
  })
}
