import { mockDXSetting } from './dxSetting.helper'
import { mockExternalLink } from './externalLink.helper'
import { mockSpace } from './community.helper'
import {
  ExternalLink,
  ExternalLinkCollection,
  ExternalLinkService
} from '../../src/modules/communities/types/externalLink'
import { ObjectId } from '../../src/modules/communities/types/id'
import * as dxSettingRepo from '../../src/modules/communities/repositories/dxSetting'
import * as externalLinkRepo from '../../src/modules/communities/repositories/externalLink'
import * as rentCafeV2Gateway from '../../src/modules/rentCafeV2/gateways/index'
import * as spaceRepo from '../../src/modules/communities/repositories/space'
import * as spaceService from '../../src/modules/communities/services/space'
import { Space } from '../../src/modules/communities/types/space'
import * as coreUtil from '@core/util'
import { RentCafeResponse } from '@modules/rentCafe/types/gateway'
import * as secretsManager from '@core/secretsManager/loadSecrets'

export let spaces: Space[] = []
export let spyOnUpdate: jest.SpyInstance<Promise<Space>>
export let spyOnGateway: jest.SpyInstance<Promise<RentCafeResponse<any>>>

export const setupMocks = (
  partialSpace: Partial<Space>,
  dxSettingHasUrl?: boolean
) => {
  const communityId = new ObjectId().toString()
  const dxSetting = mockDXSetting({
    communityId,
    rentCafeV2: {
      propertyCode: 'property-code',
      propertyId: 123123,
      apiToken: 'api-token',
      companyCode: 'company-code',
      timezone: 'America/New_York'
    }
  })
  if (dxSettingHasUrl) {
    dxSetting.rentCafeV2.url = 'rentcafev2-url-in-dxsetting'
  }
  jest.spyOn(dxSettingRepo, 'findAllDxSettings').mockResolvedValue([dxSetting])

  jest.spyOn(secretsManager, 'loadSecret').mockResolvedValue({
    RENT_CAFE_V2_API_URL: 'https://rentcafev2-url',
    CURRENT_TOKEN: 'current-token'
  })

  const partialExternalLink: Partial<ExternalLink> = {
    service: ExternalLinkService.RentCafeV2,
    collectionPath: ExternalLinkCollection.Spaces
  }
  const externalLinks = [
    mockExternalLink(partialExternalLink),
    mockExternalLink(partialExternalLink),
    mockExternalLink(partialExternalLink),
    mockExternalLink(partialExternalLink)
  ]
  jest
    .spyOn(externalLinkRepo, 'findExternalLinksByQuery')
    .mockResolvedValue(externalLinks)

  jest.spyOn(coreUtil, 'sleep').mockResolvedValue(true)

  spaces = [
    mockSpace({
      ...partialSpace,
      _id: externalLinks[0].objectId.toString(),
      unit: externalLinks[0].externalId,
      floorPlan: {
        name: 'floorplan-name-1',
        externalId: 'floorplan-1'
      },
      community: {
        _id: communityId,
        organization: {
          _id: new ObjectId(),
          name: 'org name'
        },
        name: 'community name',
        displayPriceField: ''
      }
    }),
    mockSpace({
      ...partialSpace,
      _id: externalLinks[1].objectId.toString(),
      unit: externalLinks[1].externalId,
      floorPlan: {
        name: 'floorplan-name-2',
        externalId: 'floorplan-2'
      },
      community: {
        _id: communityId,
        organization: {
          _id: new ObjectId(),
          name: 'org name'
        },
        name: 'community name',
        displayPriceField: ''
      }
    }),
    mockSpace({
      ...partialSpace,
      _id: externalLinks[2].objectId.toString(),
      unit: externalLinks[2].externalId,
      floorPlan: {
        name: 'floorplan-name-2',
        externalId: 'floorplan-2'
      },
      community: {
        _id: communityId,
        organization: {
          _id: new ObjectId(),
          name: 'org name'
        },
        name: 'community name',
        displayPriceField: ''
      },
      isModelUnit: true
    }),
    mockSpace({
      ...partialSpace,
      _id: externalLinks[3].objectId.toString(),
      unit: externalLinks[3].externalId,
      floorPlan: {
        name: 'floorplan-name-2',
        externalId: 'floorplan-2'
      },
      community: {
        _id: communityId,
        organization: {
          _id: new ObjectId(),
          name: 'org name'
        },
        name: 'community name',
        displayPriceField: ''
      },
      isModelUnit: false
    })
  ]

  jest.spyOn(spaceRepo, 'findSpacesByQuery').mockResolvedValue(spaces)
  spyOnGateway = jest
    .spyOn(rentCafeV2Gateway, 'requestToRentCafe')
    .mockResolvedValue(true)

  spyOnUpdate = jest
    .spyOn(spaceService, 'updateSpaceById')
    .mockResolvedValue({} as Space)
}
