import {
  Community,
  CommunityPlan,
  planFeatures
} from '@modules/communities/types/community'
import {
  addCommunity,
  findCommunities,
  findCommunityById,
  addOrUpdateCommunityByName,
  findCommunityByName,
  addOrUpdateCommunityById,
  getCommunityPriceFields,
  getCommunityAutoScanConfig
} from '@modules/communities/services/community'
import * as communityRepo from '@modules/communities/repositories/community'
import * as organizationRepo from '@modules/users/repositories/organization'
import * as spaceRepo from '@modules/communities/repositories/space'
import * as spaceService from '@modules/communities/services/space'
import { ObjectId, generateId } from '@modules/communities/types/id'
import { Organization } from '@modules/users/types/organization'
import { CommunityTemplate } from '@modules/communities/routes/templates/community'
import * as userRepo from '@modules/users/repositories/user'
import * as policyRepo from '@modules/users/repositories/policy'
import * as actionRepo from '@modules/users/repositories/action'
import { mockUser } from '@test/helpers/user.helper'
import { mockCommunity } from '@test/helpers/community.helper'
import { Action } from '@modules/users/types/action'
import { CommunityModel } from '@modules/communities/models/community'
import { updateCommunityByOrganizationId } from '@modules/communities/repositories/community'

describe('Community Service', () => {
  const mockCommunityData = mockCommunity()
  const mockUserData = mockUser()

  beforeEach(() => {
    jest.resetAllMocks()
  })

  it('addCommunity', async () => {
    const spyOnAddCommunity = jest
      .spyOn(communityRepo, 'addCommunity')
      .mockImplementation(() => Promise.resolve(mockCommunity()))

    const mockOrganization = {
      _id: new ObjectId('5ced1a18ff3085e86a0f2094'),
      name: 'Organization'
    }
    const spyOnFindOrganizationById = jest
      .spyOn(organizationRepo, 'findOrganizationById')
      .mockImplementation(() =>
        Promise.resolve(mockOrganization as Organization)
      )

    const spyGetOrgAdminUsersByOrg = jest
      .spyOn(userRepo, 'getOrgAdminUsersByOrg')
      .mockResolvedValue([mockUserData])

    const spyFindCommunitiesByQuery = jest
      .spyOn(communityRepo, 'findCommunitiesByQuery')
      .mockResolvedValue([mockCommunityData])

    const mockAction = {
      _id: new ObjectId('5ced1a18ff3085e86a0f2094'),
      alias: 'full',
      name: 'Action',
      description: 'Action Description'
    }

    const spyFindOneActionByAlias = jest
      .spyOn(actionRepo, 'findOneActionByAlias')
      .mockResolvedValue(mockAction as unknown as Action)

    const spyUpdateUserResourcePoliciesWithAction = jest
      .spyOn(policyRepo, 'updateUserResourcePoliciesWithAction')
      .mockResolvedValue(null)

    const spyUpdateUserById = jest
      .spyOn(userRepo, 'updateUserById')
      .mockResolvedValue(null)

    const community = await addCommunity({
      name: 'new name',
      organization: mockOrganization
    })

    expect(spyOnAddCommunity).toHaveBeenCalledTimes(1)
    expect(spyOnFindOrganizationById).toHaveBeenCalledTimes(1)
    expect(spyOnAddCommunity).toHaveBeenCalledWith({
      name: 'new name',
      organization: mockOrganization
    })
    expect(spyOnFindOrganizationById).toHaveBeenCalledTimes(1)
    expect(spyGetOrgAdminUsersByOrg).toHaveBeenCalledWith({
      organizationId: '5ced1a18ff3085e86a0f2094'
    })
    expect(spyFindCommunitiesByQuery).toHaveBeenCalledWith({
      'organization._id': '5ced1a18ff3085e86a0f2094'
    })
    expect(spyFindOneActionByAlias).toHaveBeenCalledWith('full')

    expect(spyUpdateUserResourcePoliciesWithAction).toHaveBeenCalledWith(
      mockUserData._id.toString(),
      'communities',
      [mockCommunityData._id.toString()],
      mockAction
    )
    expect(spyUpdateUserById).toHaveBeenCalledWith(
      mockUserData._id.toString(),
      {
        communities: [
          {
            _id: mockCommunityData._id.toString(),
            name: mockCommunityData.name
          }
        ]
      }
    )

    expect(community).toBeDefined()
  })

  it('findCommunityById', async () => {
    const spyOnFindCommunityById = jest
      .spyOn(communityRepo, 'findCommunityById')
      .mockImplementation(() => Promise.resolve(mockCommunity()))

    const community = await findCommunityById('123')

    expect(spyOnFindCommunityById).toHaveBeenCalledTimes(1)
    expect(spyOnFindCommunityById).toHaveBeenCalledWith('123', undefined)
    expect(community).toBeDefined()
  })

  it('findCommunityByName', async () => {
    const spyOnFindCommunityByName = jest
      .spyOn(communityRepo, 'findCommunityByName')
      .mockImplementation(() => Promise.resolve(mockCommunity()))

    const community = await findCommunityByName('Peek')

    expect(spyOnFindCommunityByName).toHaveBeenCalledTimes(1)
    expect(spyOnFindCommunityByName).toHaveBeenCalledWith('Peek')
    expect(community).toBeDefined()
  })

  it('addOrUpdateCommunityById', async () => {
    const communityId = generateId().toString()

    const spyOnAddOrUpdateCommunityById = jest
      .spyOn(communityRepo, 'addOrUpdateCommunityById')
      .mockImplementation(() =>
        Promise.resolve(mockCommunity({ _id: communityId }))
      )

    const spyOnFindCommunityByIdLean = jest
      .spyOn(communityRepo, 'findCommunityByIdLean')
      .mockImplementation(() =>
        Promise.resolve(mockCommunity({ _id: communityId }))
      )

    const spyOnUpdateSpaceCommunityById = jest
      .spyOn(spaceRepo, 'updateSpaceCommunityById')
      .mockImplementation(() => Promise.resolve(null))

    const newCommunity = {
      name: 'new name',
      leadsEmail: '<EMAIL>',
      organizationId: '123',
      plan: CommunityPlan.MarketingSuite,
      phone: '99999999',
      features: [planFeatures.Analytics],
      tours: {
        SGT: {
          isAvailable: false
        },
        AGT: {
          isAvailable: true,
          label: 'Agent Guided Tour'
        }
      }
    }
    const community = await addOrUpdateCommunityById(
      '123456789012',
      newCommunity
    )

    const communityParams = { ...newCommunity }
    delete communityParams.phone

    expect(spyOnFindCommunityByIdLean).toHaveBeenCalledTimes(1)
    expect(spyOnUpdateSpaceCommunityById).toHaveBeenCalledTimes(1)
    expect(spyOnAddOrUpdateCommunityById).toHaveBeenCalledTimes(1)
    expect(spyOnAddOrUpdateCommunityById).toHaveBeenCalledWith('123456789012', {
      ...communityParams,
      phone: '99999999'
    })
    expect(community).toBeDefined()
  })

  it('addOrUpdateCommunityByName', async () => {
    const spyOnAddOrUpdateCommunityByName = jest
      .spyOn(communityRepo, 'addOrUpdateCommunityByName')
      .mockImplementation(() => Promise.resolve({} as Community))

    const community = await addOrUpdateCommunityByName('Peek', {
      name: 'new name'
    })

    expect(spyOnAddOrUpdateCommunityByName).toHaveBeenCalledTimes(1)
    expect(spyOnAddOrUpdateCommunityByName).toHaveBeenCalledWith('Peek', {
      name: 'new name'
    })
    expect(community).toBeDefined()
  })

  describe('findCommunities', () => {
    it('should call with default options when there is no authUser', async () => {
      const spyOnFindCommunities = jest
        .spyOn(communityRepo, 'findCommunities')
        .mockImplementation(() => Promise.resolve([]))

      const spyOnCountCommunities = jest
        .spyOn(communityRepo, 'countCommunities')
        .mockImplementation(() => Promise.resolve(2))

      const organizationId = new ObjectId().toString()

      const community = await findCommunities({
        organizationIds: [organizationId]
      })

      expect(spyOnFindCommunities).toHaveBeenCalledTimes(1)
      expect(spyOnCountCommunities).toHaveBeenCalledTimes(1)
      expect(spyOnFindCommunities).toHaveBeenCalledWith(
        {
          'organization._id': { $in: [organizationId] },
          isActive: true
        },
        {
          _id: 1,
          name: 1,
          isActive: 1,
          organizationId: 1
        },
        [],
        ''
      )
      expect(spyOnCountCommunities).toHaveBeenCalledTimes(1)
      expect(community).toBeDefined()
    })

    it('should call without default options when there is authUser', async () => {
      const spyOnFindCommunities = jest
        .spyOn(communityRepo, 'findCommunities')
        .mockImplementation(() => Promise.resolve([]))

      const spyOnCountCommunities = jest
        .spyOn(communityRepo, 'countCommunities')
        .mockImplementation(() => Promise.resolve(2))

      const authUser = {
        _id: '123',
        email: '<EMAIL>',
        name: 'Admin',
        allow: [],
        deny: []
      }

      const organizationId = new ObjectId().toString()

      const community = await findCommunities(
        { organizationIds: [organizationId] },
        authUser
      )

      expect(spyOnFindCommunities).toHaveBeenCalledTimes(1)
      expect(spyOnFindCommunities).toHaveBeenCalledWith(
        { 'organization._id': { $in: [organizationId] }, isActive: true },
        {},
        [],
        ''
      )
      expect(spyOnCountCommunities).toHaveBeenCalledTimes(1)
      expect(community).toBeDefined()
    })

    describe('WHEN template exists', () => {
      describe('AND template is Linkless', () => {
        it('should call with template projection', async () => {
          const spyOnFindCommunities = jest
            .spyOn(communityRepo, 'findCommunities')
            .mockImplementation(() => Promise.resolve([]))

          const spyOnCountCommunities = jest
            .spyOn(communityRepo, 'countCommunities')
            .mockImplementation(() => Promise.resolve(2))

          const organizationId = new ObjectId().toString()

          const community = await findCommunities({
            organizationIds: [organizationId],
            template: CommunityTemplate.Linkless
          })

          expect(spyOnFindCommunities).toHaveBeenCalledTimes(1)
          expect(spyOnCountCommunities).toHaveBeenCalledTimes(1)

          expect(spyOnFindCommunities).toHaveBeenCalledWith(
            {
              'organization._id': { $in: [organizationId] },
              isActive: true
            },
            {
              _id: 1,
              name: 1,
              organizationId: 1,
              isActive: 1
            },
            [],
            '_id name'
          )
          expect(spyOnCountCommunities).toHaveBeenCalledTimes(1)
          expect(community).toBeDefined()
        })
      })
    })
  })
})

describe('getFeatureAccessCommunities', () => {
  it('should return communities with feature access', async () => {
    const spyOnFeatureAccessCommunities = jest
      .spyOn(communityRepo, 'getFeatureAccessCommunities')
      .mockImplementation(() => Promise.resolve([]))

    const matchObj = {
      'organization._id': new ObjectId('ffffffffffffffffffffffff'),
      _id: { $in: [new ObjectId('ffffffffffffffffffffffff')] },
      features: { $in: ['Analytics'] }
    }
    const communities = await communityRepo.getFeatureAccessCommunities(
      matchObj
    )
    expect(spyOnFeatureAccessCommunities).toHaveBeenCalledTimes(1)
    expect(communities).toBeDefined()
  })
})

describe('getCommunityPriceFields', () => {
  it('should return the correct price fields', async () => {
    const mockGetPriceFieldsByCommunity = jest
      .fn()
      .mockResolvedValue([
        'rentPrices.12.price',
        'pricesMetadata.12',
        'rentPrices.36.price',
        'pricesMetadata.24'
      ])
    ;(spaceService.getPriceFieldsByCommunity as jest.Mock) =
      mockGetPriceFieldsByCommunity

    const result = await getCommunityPriceFields('1')

    expect(mockGetPriceFieldsByCommunity).toHaveBeenCalledWith('1')
    expect(result).toEqual([
      'rentPrices.12.price',
      'pricesMetadata.12',
      'rentPrices.36.price',
      'pricesMetadata.24'
    ])
  })
})

describe('getCommunityAutoScanConfig', () => {
  it('should return the auto scan config', async () => {
    const mockFindCommunityByIdLean = jest.fn().mockResolvedValue({
      autoScanConfig: {
        offsetDays: 1,
        dateField: 'dateField',
        statusFields: [
          {
            field: 'field',
            values: ['value']
          }
        ]
      }
    })
    ;(communityRepo.findCommunityByIdLean as jest.Mock) =
      mockFindCommunityByIdLean

    const mockGetAutoScanFieldsByCommunity = jest.fn().mockResolvedValue({
      dateFields: ['dateField'],
      statusFields: [
        {
          field: 'field',
          values: ['value']
        }
      ]
    })
    ;(spaceService.getAutoScanFieldsByCommunity as jest.Mock) =
      mockGetAutoScanFieldsByCommunity

    const result = await getCommunityAutoScanConfig('1')

    expect(mockFindCommunityByIdLean).toHaveBeenCalledWith('1')
    expect(mockGetAutoScanFieldsByCommunity).toHaveBeenCalledWith('1')
    expect(result).toEqual({
      autoScanFields: {
        dateFields: ['dateField'],
        statusFields: [
          {
            field: 'field',
            values: ['value']
          }
        ]
      },
      autoScanConfig: {
        offsetDays: 1,
        dateField: 'dateField',
        statusFields: [
          {
            field: 'field',
            values: ['value']
          }
        ]
      }
    })
  })
})

describe('updateCommunityByOrganizationId', () => {
  beforeEach(() => {
    jest.resetAllMocks()
  })

  it('should update communities by organization ID', async () => {
    const mockData = { name: 'Updated Name' }
    const mockOrganizationId = '123456789012'
    const mockUpdateResult = { modifiedCount: 1 }

    const spyOnUpdateMany = jest
      .spyOn(CommunityModel, 'updateMany')
      .mockResolvedValue(mockUpdateResult as any)

    const result = await updateCommunityByOrganizationId(
      mockOrganizationId,
      mockData
    )

    expect(spyOnUpdateMany).toHaveBeenCalledTimes(1)
    expect(spyOnUpdateMany).toHaveBeenCalledWith(
      { 'organization._id': mockOrganizationId },
      { $set: { organization: mockData } }
    )

    expect(result).toEqual(mockUpdateResult)
  })

  it('should handle an empty update object', async () => {
    const mockOrganizationId = '123456789012'
    const mockUpdateResult = { modifiedCount: 0 }

    const spyOnUpdateMany = jest
      .spyOn(CommunityModel, 'updateMany')
      .mockResolvedValue(mockUpdateResult as any)

    const result = await updateCommunityByOrganizationId(mockOrganizationId, {})

    expect(spyOnUpdateMany).not.toHaveBeenCalled()
    expect(result).toEqual(mockUpdateResult)
  })

  it('should handle errors from updateMany', async () => {
    const mockOrganizationId = '123456789012'
    const mockData = { name: 'Updated Name' }
    const mockError = new Error('Update failed')

    const spyOnUpdateMany = jest
      .spyOn(CommunityModel, 'updateMany')
      .mockRejectedValue(mockError)

    await expect(
      updateCommunityByOrganizationId(mockOrganizationId, mockData)
    ).rejects.toThrow('Update failed')
    expect(spyOnUpdateMany).toHaveBeenCalledTimes(1)
  })
})
