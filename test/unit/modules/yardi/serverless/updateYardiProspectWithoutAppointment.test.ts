import * as dxSettingService from '@modules/communities/services/dxSetting'
import * as prospectService from '@modules/prospects/services/prospect'
import { handler } from '@modules/yardi/serverless/updateYardiProspectWithoutAppointment'
import * as yardiService from '@modules/yardi/services/yardi'

import { generateId } from '@modules/communities/types/id'
import * as prospectSyncService from '@modules/prospects/services/prospectSgtSync'

jest.mock('@core/db')

describe('Yardi', () => {
  const prospectId = generateId()
  const dxSettingId = generateId()

  beforeEach(() => {
    jest.spyOn(yardiService, 'fetchProspectByEmail').mockResolvedValue({
      id: 'yardi-prospect-id',
      email: '<EMAIL>'
    })
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Serverless', () => {
    describe('Update Yardi Prospect Without Appointment', () => {
      describe('WHEN send prospect updates successfully', () => {
        it('should log the updated prospect', async () => {
          const dxSetting = { id: dxSettingId } as any
          const prospect = { id: prospectId } as any

          jest
            .spyOn(prospectSyncService, 'updateOneProspectSgtSync')
            .mockResolvedValue(undefined)

          jest
            .spyOn(dxSettingService, 'findDXSettingById')
            .mockResolvedValue(dxSetting)
          jest
            .spyOn(prospectService, 'findProspectById')
            .mockResolvedValue(prospect)

          const spy = jest
            .spyOn(yardiService, 'sendProspect')
            .mockResolvedValue(undefined)
          jest.spyOn(console, 'log')

          await handler({
            Records: [
              {
                body: JSON.stringify({ prospectId, dxSettingId })
              }
            ]
          } as any)

          expect(spy).toHaveBeenCalledWith(
            prospect,
            dxSetting,
            'PEEK_NO_SGT_BOOKED',
            false
          )
        })
      })

      describe('WHEN send prospect fails', () => {
        it('should log the error', async () => {
          const dxSetting = { id: dxSettingId } as any
          const prospect = { id: prospectId } as any

          jest
            .spyOn(dxSettingService, 'findDXSettingById')
            .mockResolvedValue(dxSetting)
          jest
            .spyOn(prospectService, 'findProspectById')
            .mockResolvedValue(prospect)

          const expectedError = new Error('Failed to send prospect')
          jest
            .spyOn(yardiService, 'sendProspect')
            .mockRejectedValue(expectedError)

          try {
            await handler({
              Records: [
                {
                  body: JSON.stringify({ prospectId, dxSettingId })
                }
              ]
            } as any)
          } catch (err) {
            expect(err).toBe(expectedError)
            return
          }

          expect(true).toBeFalsy()
        })
      })

      describe('WHEN prospect is not found', () => {
        it('should not send prospect', async () => {
          const dxSetting = { id: dxSettingId } as any
          const prospect = { id: prospectId } as any

          jest
            .spyOn(prospectSyncService, 'updateOneProspectSgtSync')
            .mockResolvedValue(undefined)

          jest
            .spyOn(dxSettingService, 'findDXSettingById')
            .mockResolvedValue(dxSetting)
          jest
            .spyOn(prospectService, 'findProspectById')
            .mockResolvedValue(prospect)

          jest
            .spyOn(yardiService, 'fetchProspectByEmail')
            .mockResolvedValue(null)

          const spy = jest
            .spyOn(yardiService, 'sendProspect')
            .mockResolvedValue(undefined)
          jest.spyOn(console, 'log')

          await handler({
            Records: [
              {
                body: JSON.stringify({ prospectId, dxSettingId })
              }
            ]
          } as any)

          expect(spy).not.toHaveBeenCalled()
        })
      })
    })
  })
})
