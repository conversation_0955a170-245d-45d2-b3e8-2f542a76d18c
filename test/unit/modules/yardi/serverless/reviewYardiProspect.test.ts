import * as publishSqs from '@core/sqs'
import { handler } from '@modules/yardi/serverless/reviewYardiProspect'
import { generateId } from '@modules/communities/types/id'
import * as prospectSgtSyncService from '@modules/prospects/services/prospectSgtSync'

describe('Yardi', () => {
  const prospectId = generateId()
  const dxSettingId = generateId()
  const spaceId = generateId()

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Serverless', () => {
    describe('Review Yardi Prospect', () => {
      describe('#handler', () => {
        describe('WHEN has prospects to review', () => {
          it('should publish the prospects to the YARDI_UPDATE_COMMENTS_QUEUE', async () => {
            const sgtProspectId = generateId()
            const findManyProspectSgtSyncSpy = jest
              .spyOn(prospectSgtSyncService, 'findManyProspectSgtSync')
              .mockResolvedValue([
                {
                  _id: sgtProspectId.toString(),
                  prospect: prospectId.toString(),
                  dxSetting: dxSettingId.toString(),
                  createdAt: new Date(),
                  service: 'YARDI',
                  reviewed: false,
                  walkabout: null,
                  space: spaceId.toString()
                }
              ])

            const publishSpy = jest
              .spyOn(publishSqs, 'publish')
              .mockResolvedValue(undefined)

            await handler()

            expect(publishSpy).toHaveBeenCalledWith(
              {
                prospectId: prospectId.toString(),
                dxSettingId: dxSettingId.toString(),
                syncId: sgtProspectId.toString()
              },
              'YARDI_UPDATE_COMMENTS_QUEUE',
              prospectId.toString(),
              expect.any(String)
            )
            expect(findManyProspectSgtSyncSpy).toHaveBeenCalledWith({
              createdAt: { $lte: expect.any(Date), $gte: expect.any(Date) },
              service: 'yardi',
              reviewed: false,
              walkabout: null
            })
          })
        })

        describe('WHEN has no prospects to review', () => {
          it('should not publish any prospect to the YARDI_UPDATE_COMMENTS_QUEUE', async () => {
            const findManyProspectSgtSyncSpy = jest
              .spyOn(prospectSgtSyncService, 'findManyProspectSgtSync')
              .mockResolvedValue([])

            const publishSpy = jest
              .spyOn(publishSqs, 'publish')
              .mockResolvedValue(undefined)

            await handler()

            expect(publishSpy).not.toHaveBeenCalled()
            expect(findManyProspectSgtSyncSpy).toHaveBeenCalledWith({
              createdAt: { $lte: expect.any(Date), $gte: expect.any(Date) },
              service: 'yardi',
              reviewed: false,
              walkabout: null
            })
          })
        })
      })
    })
  })
})
