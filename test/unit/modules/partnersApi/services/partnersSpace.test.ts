import { getSpacesByCommunityId } from '@modules/partnersApi/services/partnersSpace'
import * as spacesRepo from '@modules/communities/repositories/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import { ExternalLinkService } from '@modules/communities/types/externalLink'

jest.mock('@modules/communities/repositories/space')

const WEB_VIEWER_BASE_URL =
  process.env.WEB_VIEWER_BASE_URL || 'https://a.dev.peek.us/'

describe('partnersSpace', () => {
  describe('getSpacesByCommunityId', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should return complete space response with all attributes for unit space', async () => {
      const mockSpaces = [
        {
          _id: 'space-id-1',
          token: 'test-token-123',
          type: SpaceType.Unit,
          bedrooms: 2,
          bathrooms: 1,
          unit: '101A',
          building: {
            _id: 'building-id-456',
            address: {
              street: '123 Test St',
              city: 'Test City',
              state: 'TS',
              postalCode: '12345',
              latitude: 37.7749,
              longitude: -122.4194,
              country: 'US'
            },
            externalLinks: [
              {
                externalId: 'building-ext-id-1',
                service: ExternalLinkService.Entrata,
                externalName: 'building-name'
              }
            ]
          },
          isModelUnit: true,
          floorPlan: {
            name: 'Studio Deluxe',
            externalId: 'studio-deluxe-001'
          },
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'ext-id-1',
              service: ExternalLinkService.Entrata,
              externalName: 'test-name'
            },
            {
              externalId: 'ext-id-2',
              service: ExternalLinkService.ApartmentsDotCom,
              externalName: null
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)

      const space = result[0]

      // Test all top-level attributes
      expect(space.id).toBe('space-id-1')
      expect(space.unitName).toBe('101A')
      expect(space.virtualTourUrl).toBe(
        `${WEB_VIEWER_BASE_URL}?token=test-token-123&pageType=unit`
      )
      expect(space.type).toBe(SpaceType.Unit)
      expect(space.completed).toBe(true)
      expect(space.visible).toBe(true)

      // Test address object
      expect(space.address).toEqual({
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        latitude: 37.7749,
        longitude: -122.4194,
        country: 'US'
      })

      // Test externalIds array
      expect(space.externalIds).toHaveLength(2)
      expect(space.externalIds[0]).toEqual({
        id: 'ext-id-1',
        service: 'entrata',
        name: 'test-name'
      })
      expect(space.externalIds[1]).toEqual({
        id: 'ext-id-2',
        service: 'apartmentsDotCom',
        name: null
      })

      // Test unitDetails object (only present for unit spaces)
      expect(space.unitDetails).toBeDefined()
      expect(space.unitDetails?.layout).toBe('2br/1ba')
      expect(space.unitDetails?.layoutType).toBe('Studio Deluxe')
      expect(space.unitDetails?.isModelUnit).toBe(true)

      // Test building object
      expect(space.building).toBeDefined()
      expect(space.building._id).toBe('building-id-456')
      expect(space.building.externalIds).toHaveLength(1)
      expect(space.building.externalIds[0]).toEqual({
        id: 'building-ext-id-1',
        service: 'entrata',
        name: 'building-name'
      })
    })

    it('should return null layoutType when floorPlan.name is not available', async () => {
      const mockSpaces = [
        {
          _id: 'space-id-2',
          token: 'test-token-456',
          type: SpaceType.Unit,
          bedrooms: 1,
          bathrooms: 1,
          unit: '202B',
          building: {
            _id: 'building-id-789',
            address: {
              street: '456 Another St',
              city: 'Another City',
              state: 'AC',
              postalCode: '67890'
            }
          },
          isModelUnit: false,
          // No floorPlan field
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: false,
          isVisible: false,
          externalLinks: [
            {
              externalId: 'ext-id-3',
              service: ExternalLinkService.Entrata,
              externalName: 'test-name-2'
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)

      const space = result[0]

      // Test all attributes when floorPlan is not available
      expect(space.id).toBe('space-id-2')
      expect(space.unitName).toBe('202B')
      expect(space.virtualTourUrl).toBe(
        `${WEB_VIEWER_BASE_URL}?token=test-token-456&pageType=unit`
      )
      expect(space.type).toBe(SpaceType.Unit)
      expect(space.completed).toBe(false)
      expect(space.visible).toBe(false)

      // Test unitDetails with null layoutType
      expect(space.unitDetails).toBeDefined()
      expect(space.unitDetails?.layout).toBe('1br/1ba')
      expect(space.unitDetails?.layoutType).toBe(null)
      expect(space.unitDetails?.isModelUnit).toBe(false)
    })

    it('should return complete response for amenity space without unitDetails', async () => {
      const mockSpaces = [
        {
          _id: 'amenity-id-1',
          token: 'amenity-token-789',
          type: SpaceType.Amenity,
          unit: 'Pool Area',
          building: {
            _id: 'building-id-amenity',
            address: {
              street: '789 Amenity Blvd',
              city: 'Amenity City',
              state: 'AM',
              postalCode: '11111'
            }
          },
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'amenity-ext-id-1',
              service: ExternalLinkService.RealPage,
              externalName: 'pool-area'
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)

      const space = result[0]

      // Test amenity space attributes
      expect(space.id).toBe('amenity-id-1')
      expect(space.unitName).toBe('Pool Area')
      expect(space.virtualTourUrl).toBe(
        `${WEB_VIEWER_BASE_URL}?token=amenity-token-789&pageType=unit`
      )
      expect(space.type).toBe(SpaceType.Amenity)
      expect(space.completed).toBe(true)
      expect(space.visible).toBe(true)

      // Amenity spaces should NOT have unitDetails
      expect(space.unitDetails).toBeUndefined()

      // Should still have building and externalIds
      expect(space.building).toBeDefined()
      expect(space.externalIds).toHaveLength(1)
      expect(space.externalIds[0]).toEqual({
        id: 'amenity-ext-id-1',
        service: 'realpage',
        name: 'pool-area'
      })
    })

    it('should handle multiple spaces with different types and attributes', async () => {
      const mockSpaces = [
        {
          _id: 'unit-space-1',
          token: 'unit-token-1',
          type: SpaceType.Unit,
          bedrooms: 3,
          bathrooms: 2,
          unit: '301',
          building: {
            _id: 'building-1',
            address: {
              street: '100 Multi St',
              city: 'Multi City',
              state: 'MC',
              postalCode: '22222'
            }
          },
          isModelUnit: false,
          floorPlan: {
            name: '3BR Luxury',
            externalId: 'luxury-3br'
          },
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'unit-ext-1',
              service: ExternalLinkService.Entrata,
              externalName: 'unit-1'
            }
          ]
        },
        {
          _id: 'amenity-space-1',
          token: 'amenity-token-1',
          type: SpaceType.Amenity,
          unit: 'Gym',
          building: {
            _id: 'building-1',
            address: {
              street: '100 Multi St',
              city: 'Multi City',
              state: 'MC',
              postalCode: '22222'
            }
          },
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'amenity-ext-1',
              service: ExternalLinkService.RealPage,
              externalName: 'gym'
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(2)

      // Test unit space
      const unitSpace = result[0]
      expect(unitSpace.type).toBe(SpaceType.Unit)
      expect(unitSpace.unitDetails).toBeDefined()
      expect(unitSpace.unitDetails?.layout).toBe('3br/2ba')
      expect(unitSpace.unitDetails?.layoutType).toBe('3BR Luxury')
      expect(unitSpace.unitDetails?.isModelUnit).toBe(false)

      // Test amenity space
      const amenitySpace = result[1]
      expect(amenitySpace.type).toBe(SpaceType.Amenity)
      expect(amenitySpace.unitDetails).toBeUndefined()
      expect(amenitySpace.unitName).toBe('Gym')
    })

    it('should handle spaces with empty or missing external links', async () => {
      const mockSpaces = [
        {
          _id: 'space-no-links',
          token: 'token-no-links',
          type: SpaceType.Unit,
          bedrooms: 1,
          bathrooms: 1,
          unit: '101',
          building: {
            _id: 'building-no-links',
            address: {
              street: '123 No Links St',
              city: 'No Links City',
              state: 'NL',
              postalCode: '33333'
            }
          },
          isModelUnit: false,
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [] // Empty external links array
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)

      const space = result[0]
      expect(space.externalIds).toHaveLength(0)
      expect(space.externalIds).toEqual([])

      // Other attributes should still be present
      expect(space.id).toBe('space-no-links')
      expect(space.unitDetails?.layoutType).toBe(null) // No floorPlan
    })
  })
})
