import {
  sendAppointment,
  getAvailableDays,
  getAvailableTimes
} from '@modules/dataExchange/services/vendorsV2Functions'
import { generateId } from '@modules/communities/types/id'
import { DXSetting } from '@modules/communities/types/dxSetting'
import { Prospect } from '@modules/prospects/types/prospect'
import { Appointment } from '@modules/prospects/types/appointment'
import { TourType } from '@modules/anyoneHome/types/getTourAvailability'
import * as peekInternalApiClient from '@core/request/peekInternalApiClient'

jest.mock('@core/request/peekInternalApiClient')

describe('VendorsV2Functions', () => {
  const dxSettingId = generateId()
  const prospectId = generateId()
  const appointmentId = generateId()
  const communityId = generateId()

  const mockDxSetting = {
    _id: dxSettingId.toString(),
    communityId: communityId.toString(),
    service: 'entrata',
    dataExchange: { syncProspect: true }
  } as DXSetting

  const mockProspect: Prospect = {
    _id: prospectId,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '123456789',
    moveInDate: new Date('2024-01-01')
  } as Prospect

  const mockAppointment: Appointment = {
    _id: appointmentId,
    prospectId,
    date: '2024-01-15',
    startTime: '10:00:00'
  } as Appointment

  afterEach(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  describe('#sendAppointment', () => {
    it('should send appointment successfully', async () => {
      const expectedResult = { success: true }
      const makeInternalApiV2RequestSpy = jest
        .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
        .mockResolvedValueOnce(expectedResult)

      const result = await sendAppointment(
        mockDxSetting,
        mockProspect,
        mockAppointment
      )

      expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
        '/dx-settings/entrata/send-appointment',
        'POST',
        {
          dxSettingId: dxSettingId.toString(),
          prospect: {
            firstName: mockProspect.firstName,
            lastName: mockProspect.lastName,
            email: mockProspect.email,
            phone: mockProspect.phone,
            moveInDate: mockProspect.moveInDate,
            prospectId: prospectId.toString()
          },
          appointmentId: appointmentId.toString(),
          date: '2024-01-15',
          time: '10:00:00'
        }
      )
      expect(result).toEqual(expectedResult)
    })

    it('should handle camelCase service names correctly', async () => {
      const gsDynamicsDxSetting = {
        ...mockDxSetting,
        service: 'gsDynamics'
      }
      const expectedResult = { success: true }
      const makeInternalApiV2RequestSpy = jest
        .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
        .mockResolvedValueOnce(expectedResult)

      await sendAppointment(gsDynamicsDxSetting, mockProspect, mockAppointment)

      expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
        '/dx-settings/gsDynamics/send-appointment',
        'POST',
        {
          dxSettingId: dxSettingId.toString(),
          prospect: {
            firstName: mockProspect.firstName,
            lastName: mockProspect.lastName,
            email: mockProspect.email,
            phone: mockProspect.phone,
            moveInDate: mockProspect.moveInDate,
            prospectId: prospectId.toString()
          },
          appointmentId: appointmentId.toString(),
          date: '2024-01-15',
          time: '10:00:00'
        }
      )
    })
  })

  describe('#getAvailableDays', () => {
    describe('when called with default parameters', () => {
      it('should use default values for numberOfDays and tourType', async () => {
        const expectedResult = {
          dates: ['2024-01-15', '2024-01-16'],
          timezone: 'America/New_York'
        }
        const makeInternalApiV2RequestSpy = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce(expectedResult)

        const result = await getAvailableDays(mockDxSetting)

        expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
          '/dx-settings/entrata/available-days',
          'GET',
          undefined,
          undefined,
          {
            numberOfDays: '7',
            tourType: TourType.GT,
            dxSettingId: dxSettingId.toString()
          }
        )
        expect(result).toEqual(expectedResult)
      })
    })

    describe('when called with custom parameters', () => {
      it('should use provided numberOfDays and tourType', async () => {
        const expectedResult = {
          dates: ['2024-01-15', '2024-01-16', '2024-01-17'],
          timezone: 'America/New_York'
        }
        const makeInternalApiV2RequestSpy = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce(expectedResult)

        const result = await getAvailableDays(mockDxSetting, 14, TourType.GT)

        expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
          '/dx-settings/entrata/available-days',
          'GET',
          undefined,
          undefined,
          {
            numberOfDays: '14',
            tourType: TourType.GT,
            dxSettingId: dxSettingId.toString()
          }
        )
        expect(result).toEqual(expectedResult)
      })
    })

    describe('when numberOfDays is 0', () => {
      it('should use default value of 7', async () => {
        const expectedResult = {
          dates: ['2024-01-15'],
          timezone: 'America/New_York'
        }
        const makeInternalApiV2RequestSpy = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce(expectedResult)

        await getAvailableDays(mockDxSetting, 0)

        expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
          '/dx-settings/entrata/available-days',
          'GET',
          undefined,
          undefined,
          {
            numberOfDays: '7',
            tourType: TourType.GT,
            dxSettingId: dxSettingId.toString()
          }
        )
      })
    })
  })

  describe('#getAvailableTimes', () => {
    describe('when called with default tourType', () => {
      it('should use GT as default tourType', async () => {
        const expectedResult = {
          times: [
            { startTime: '09:00:00', endTime: '09:30:00' },
            { startTime: '10:00:00', endTime: '10:30:00' }
          ],
          timezone: 'America/New_York'
        }
        const makeInternalApiV2RequestSpy = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce(expectedResult)

        const result = await getAvailableTimes(mockDxSetting, '2024-01-15')

        expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
          '/dx-settings/entrata/available-times',
          'GET',
          undefined,
          undefined,
          {
            date: '2024-01-15',
            tourType: TourType.GT,
            dxSettingId: dxSettingId.toString()
          }
        )
        expect(result).toEqual(expectedResult)
      })
    })

    describe('when called with custom tourType', () => {
      it('should use provided tourType', async () => {
        const expectedResult = {
          times: [{ startTime: '14:00:00', endTime: '15:00:00' }],
          timezone: 'America/New_York'
        }
        const makeInternalApiV2RequestSpy = jest
          .spyOn(peekInternalApiClient, 'makeInternalApiV2Request')
          .mockResolvedValueOnce(expectedResult)

        const result = await getAvailableTimes(
          mockDxSetting,
          '2024-01-15',
          TourType.GT
        )

        expect(makeInternalApiV2RequestSpy).toHaveBeenCalledWith(
          '/dx-settings/entrata/available-times',
          'GET',
          undefined,
          undefined,
          {
            date: '2024-01-15',
            tourType: TourType.GT,
            dxSettingId: dxSettingId.toString()
          }
        )
        expect(result).toEqual(expectedResult)
      })
    })
  })
})
