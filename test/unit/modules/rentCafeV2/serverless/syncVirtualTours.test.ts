import * as serverless from '@modules/rentCafeV2/serverless/syncVirtualTours'
import * as service from '@modules/rentCafeV2/services/syncVirtualTours'
import * as rentCafeService from '@modules/rentCafeV2/services/'
import * as log from '@core/log'

describe('RentCafeV2 - Sync Virtual Tours - serverlesss', () => {
  it('Execute lambda', async () => {
    const spyOnLoadSecrets = jest
      .spyOn(rentCafeService, 'loadRentCafeV2Secrets')
      .mockResolvedValue(true)

    const spyOnSyncVirtualTours = jest
      .spyOn(service, 'syncVirtualTours')
      .mockResolvedValue()

    const result = await serverless.handler()

    expect(result).toEqual({
      message: 'OK',
      statusCode: 200
    })

    expect(spyOnLoadSecrets).toHaveBeenCalled()
    expect(spyOnSyncVirtualTours).toHaveBeenCalled()
  })

  it('Execute lambda - failure', async () => {
    const spyOnLoadSecrets = jest
      .spyOn(rentCafeService, 'loadRentCafeV2Secrets')
      .mockResolvedValue(true)

    const spyOnSyncVirtualTours = jest
      .spyOn(service, 'syncVirtualTours')
      .mockRejectedValue(new Error('Error message'))

    const spyOnLogError = jest.spyOn(log, 'logError').mockReturnValue()

    const result = await serverless.handler()

    expect(result).toEqual({
      message: 'OK',
      statusCode: 200
    })

    expect(spyOnLoadSecrets).toHaveBeenCalled()
    expect(spyOnSyncVirtualTours).toHaveBeenCalled()
    expect(spyOnLogError).toHaveBeenCalledWith(
      'sync-rent-cafe-v2-virtual-tours',
      'Error syncing',
      { error: 'Error message' }
    )
  })
})
