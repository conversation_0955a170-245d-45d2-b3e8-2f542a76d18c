name: PR Pipeline

on:
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  install_dependencies:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    name: Install dependencies

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn install
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

  tsc:
    name: Typescript Check
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: TSC check
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: ts:check

  lint:
    name: <PERSON><PERSON>
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: <PERSON><PERSON>
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: lint

  test:
    name: Unit tests
    needs: [tsc, lint]
    runs-on: 8_32_runner
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: TSC check
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: test:unit:report

      - name: Jest Coverage Comment
        uses: MishaKav/jest-coverage-comment@main
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          coverage-summary-path: ./coverage/coverage-summary.json
          badge-title: Coverage
          title: Code coverage
          summary-title: Summary
          hide-comment: false
          create-new-comment: false
          hide-summary: false


