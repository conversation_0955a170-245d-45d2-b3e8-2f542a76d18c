/**
 * @type {import('eslint').Rule.RuleModule}
 */
module.exports = {
  meta: {
    messages: {
      noMoment: 'Using moment.js is not allowed. Use date-fns instead.'
    }
  },
  create: (context) => {
    return {
      ImportDeclaration(node) {
        if (!node.source) return
        const source = node.source.value.trim()
        if (['moment-timezone', 'moment'].includes(source)) {
          context.report({
            node,
            messageId: 'noMoment'
          })
        }
      }
    }
  }
}
