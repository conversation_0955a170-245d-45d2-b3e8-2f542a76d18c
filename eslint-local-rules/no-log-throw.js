/**
 * @type {import('eslint').Rule.RuleModule}
 */
module.exports = {
  meta: {
    messages: {
      noLogThrow:
        "Don't log and throw in the same catch block. Prefer logging only at the top level."
    }
  },
  create(context) {
    return {
      CatchClause(node) {
        let throwNode = null
        let logNode = null

        for (const stmt of node.body.body) {
          if (stmt.type === 'ThrowStatement') {
            throwNode = stmt
          }

          if (
            stmt.type === 'ExpressionStatement' &&
            stmt.expression.type === 'CallExpression'
          ) {
            const expr = stmt.expression
            const callee = expr.callee

            if (
              callee.type === 'MemberExpression' &&
              callee.object.type === 'Identifier' &&
              callee.property.type === 'Identifier'
            ) {
              const obj = callee.object.name
              const method = callee.property.name

              if (
                (obj === 'console' && method === 'error') ||
                (obj === 'Sentry' && method === 'captureException')
              ) {
                logNode = stmt
              }
            }

            if (callee.type === 'Identifier' && callee.name === 'logError') {
              logNode = stmt
            }
          }
        }

        if (throwNode && logNode) {
          context.report({
            node,
            messageId: 'noLogThrow',
            loc: {
              start: throwNode.loc.start,
              end: throwNode.loc.end
            }
          })
          context.report({
            node,
            messageId: 'noLogThrow',
            loc: {
              start: logNode.loc.start,
              end: logNode.loc.end
            }
          })
        }
      }
    }
  }
}
