{"name": "peek-backend", "version": "1.0.0", "description": "New Peek Backend Project", "main": "dist/api/server.js", "repository": "https://gitlab.com/piiq-developers/peek-backend", "author": "<EMAIL>", "private": true, "scripts": {"build": "rm -rf dist/ && node --max-old-space-size=8192 node_modules/.bin/tsc --build --force tsconfig.json && node --max-old-space-size=8192 node_modules/.bin/tscpaths -p tsconfig.json -s ./src -o ./dist", "dev": "yarn start:api:local", "lint": "eslint . --ext .ts", "ts:check": "node --max-old-space-size=8192 ./node_modules/.bin/tsc --incremental", "lint:fix": "eslint . --fix --ext .ts", "prettier:fix": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "lint-prettier": "yarn run lint:fix && yarn run prettier:fix", "start:api": "NODE_ENV=local node dist/api/server.js", "start:api:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --transpile-only --respawn --ignore-watch node_modules src/api/server.ts", "start:serverless:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/modules/partnersApi/index.ts", "start:entrata:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/modules/entrata/services/entrata.ts", "start:yardi:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/modules/yardi/serverless/yardiWorkerExec.ts", "start:funnel:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/modules/funnel/services/parser.ts", "fixScan": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/scripts/fix-scan-request-floor-plans.ts", "migrateScanDates": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/scripts/migrate-legacy-scan-dates.ts", "consume:funnel:local": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/modules/funnel/services/consumeAndSave.ts", "etl:run": "NODE_ENV=local ts-node-dev -r tsconfig-paths/register --respawn --ignore-watch node_modules src/scripts/etl/index.ts", "test": "yarn test:unit && yarn test:e2e", "test:e2e": "yarn restore:test && NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --config ./jest-e2e.ts --runInBand", "test:e2e:watch": "yarn restore:test && NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --config ./jest-e2e.ts --watchAll", "test:e2e:coverage": "yarn restore:test &&  NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --config ./jest-e2e.ts --coverage", "test:debug": "NODE_ENV=test node --inspect-brk --max-old-space-size=8192 node_modules/.bin/jest --runInBand --no-coverage", "test:unit": "SUPPRESS_JEST_WARNINGS=true TEST_ENV=unit NODE_ENV=test node --max-old-space-size=8192 node_modules/jest/bin/jest.js --bail", "test:unit:report": "SUPPRESS_JEST_WARNINGS=true TEST_ENV=unit NODE_ENV=test node --max-old-space-size=8192 node_modules/jest/bin/jest.js --bail --coverage --coverageReporters=json-summary", "test:unit:watch": "NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --watch", "test:unit:watch:debug": "NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --watchAll -i --testTimeout 600000", "test:unit:local": "SUPPRESS_JEST_WARNINGS=true TEST_ENV=unit NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --bail --runInBand --onlyChanged", "test:unit:coverage": "NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest -i --coverage", "test:unit:watch:local": "NODE_ENV=test_local node --max-old-space-size=8192 node_modules/.bin/jest --watchAll -i", "test:unit:coverage:local": "NODE_ENV=test_local node --max-old-space-size=8192 node_modules/.bin/jest -i --coverage", "test:transform:community": "NODE_ENV=test_local node --max-old-space-size=8192 node_modules/.bin/jest communityTransform.test --config ./jest-e2e.ts", "test:integration": "NODE_ENV=test node --max-old-space-size=8192 node_modules/.bin/jest --config ./jest-integration.ts", "generate:access-device-to-mermaid": "NODE_ENV=local ts-node -r tsconfig-paths/register src/scripts/access-device-to-mermaid.ts", "test:e2e:sgt:local": "NODE_ENV=test_local node --max-old-space-size=8192 node_modules/.bin/jest modules/sgt --config ./jest-e2e.ts", "test:e2e:tours:local": "NODE_ENV=test_local node --max-old-space-size=8192 node_modules/.bin/jest modules/sgt/tours.e2e.test --config ./jest-e2e.ts", "dc:up": "docker-compose up --build -d", "dc:up:test": "docker-compose -f docker-compose.test.yml up --build -d", "dc:down": "docker-compose down", "dc:down:test": "docker-compose -f docker-compose.test.yml down", "sls:local": "sls offline start --stage local", "prepare": "husky install", "invoke:local": "yarn serverless invoke local --function", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org peek-technologies --project backend ./dist && sentry-cli sourcemaps upload --org peek-technologies --project backend ./dist"}, "dependencies": {"@aws-crypto/sha256-js": "^5.2.0", "@aws-sdk/client-cloudfront": "^3.515.0", "@aws-sdk/client-cognito-identity": "^3.391.0", "@aws-sdk/client-lambda": "^3.391.0", "@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/client-secrets-manager": "^3.438.0", "@aws-sdk/client-ses": "^3.391.0", "@aws-sdk/client-sns": "^3.391.0", "@aws-sdk/client-sqs": "^3.391.0", "@aws-sdk/client-sts": "^3.714.0", "@aws-sdk/credential-provider-cognito-identity": "^3.391.0", "@aws-sdk/credential-provider-node": "^3.637.0", "@aws-sdk/credential-providers": "^3.391.0", "@aws-sdk/protocol-http": "^3.374.0", "@aws-sdk/s3-request-presigner": "^3.391.0", "@aws-sdk/signature-v4": "^3.374.0", "@piiqtechnologies/peek-secrets-loader": "1.3.2", "@sentry/cli": "^2.42.4", "@sentry/node": "^9.9.0", "@sentry/serverless": "^7.120.3", "@types/dot-object": "^2.1.2", "aws4": "^1.12.0", "axios": "^0.27.2", "axios-retry": "^3.4.0", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "body-parser": "^1.20.0", "bottleneck": "^2.19.5", "connect-mongo": "^4.6.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-stringify": "^6.4.5", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "dot-object": "^2.1.4", "dotenv": "^16.0.1", "easy-soap-request": "^5.2.0", "express": "^4.18.1", "express-async-errors": "^3.1.1", "express-http-context": "^1.2.4", "express-session": "^1.17.3", "ftp": "^0.3.10", "googleapis": "^110.0.0", "helmet": "^6.0.0", "joi": "^17.6.0", "joi-to-swagger": "^6.1.1", "jsonwebtoken": "^8.5.1", "md5": "^2.3.0", "mjml": "^4.13.0", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "mongoose": "^8", "nanoid": "^3.0.0", "node-cron": "^3.0.2", "nodemailer": "^6.8.0", "p-limit": "3", "passport": "^0.6.0", "passport-auth0": "^1.4.3", "passport-custom": "^1.1.1", "pixelmatch": "^5.3.0", "prettier": "^2.7.1", "querystring": "^0.2.1", "seam": "^1.75.1", "serverless-http": "^3.0.2", "serverless-local-schedule": "^0.1.7", "ssh2-sftp-client": "^11.0.0", "swagger-ui-express": "^4.5.0", "xml-js": "^1.6.11", "zod": "^3.22.4"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@types/aws-lambda": "^8.10.126", "@types/aws4": "^1.11.6", "@types/bcryptjs": "^2.4.2", "@types/bluebird": "^3.5.37", "@types/cors": "^2.8.12", "@types/easy-soap-request": "^4.1.1", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/ftp": "^0.3.33", "@types/helmet": "^4.0.0", "@types/jest": "^29.5.14", "@types/jest-image-snapshot": "^6.4.0", "@types/jsonwebtoken": "^8.5.9", "@types/md5": "^2.3.2", "@types/moment-timezone": "0.5.30", "@types/mongoose": "^5.11.97", "@types/nock": "^11.1.0", "@types/node": "*", "@types/node-cron": "^3.0.2", "@types/nodemailer": "^6.4.6", "@types/passport": "^1.0.16", "@types/passport-auth0": "^1.0.5", "@types/ssh2-sftp-client": "^9.0.4", "@types/supertest": "^2.0.12", "@types/swagger-ui-express": "^4.1.6", "@types/xml-js": "^1.0.0", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "esbuild": "^0.17.19", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-local-rules": "^2.0.1", "eslint-plugin-prettier": "^4.2.1", "expect": "^29.5.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-mock-axios": "^4.8.0", "lint-staged": "^13.0.3", "nock": "^13.2.9", "node-loader": "^2.0.0", "openapi-types": "^12.0.0", "serverless": "^3.27.0", "serverless-dotenv-plugin": "^4.0.2", "serverless-esbuild": "^1.45.0", "serverless-localstack": "^1.0.1", "serverless-offline": "^9.3.0", "supertest": "^6.2.4", "ts-jest": "^29.3.4", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.1.0", "tscpaths": "^0.0.9", "typescript": "^5.8.3", "typescript-transform-paths": "^3.3.1"}, "env": {"test": {"plugins": ["transform-es2015-modules-commonjs"]}}, "license": "MIT", "engines": {"node": "^16.15.0", "yarn": "3.5.1"}, "lint-staged": {"**/*.{js,ts}": ["npx prettier --write", "npx eslint --fix"]}, "packageManager": "yarn@3.5.1"}