import { loadEnv } from '../src/core/loadEnv'
import { CommunityModel } from '../src/modules/communities/models/community'
import { findOrganizationByName } from '../src/modules/users/services/organization'

loadEnv()

const getData = async () => {
  const [peekOrganization, organizationA, organizationB] = await Promise.all(
    ['Peek', 'Organization A', 'Organization B'].map(async (org) => {
      return await findOrganizationByName(org)
    })
  )

  return [
    {
      name: 'First Community',
      organization: {
        _id: peekOrganization._id,
        name: peekOrganization.name
      }
    },
    {
      name: 'Second Community',
      organization: {
        _id: peekOrganization._id,
        name: peekOrganization.name
      }
    },
    {
      name: 'Community Org A',
      organization: {
        _id: organizationA._id,
        name: organizationA.name
      }
    },
    {
      name: 'Community Org A2',
      organization: {
        _id: organizationA._id,
        name: organizationA.name
      }
    },
    {
      name: 'Community Org B',
      organization: {
        _id: organizationB._id,
        name: organizationB.name
      }
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()
    const promises = await data.map(async (item) => {
      const existing = await CommunityModel.findOne({
        name: item.name
      }).lean()

      return existing
        ? await CommunityModel.updateOne({ _id: existing._id }, item)
        : await CommunityModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.log(err)
  } finally {
    console.log('Communities seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
