import { faker } from '@faker-js/faker'
import { loadEnv } from '../src/core/loadEnv'
import { BuildingModel } from '../src/modules/communities/models/building'
import { CommunityModel } from '../src/modules/communities/models/community'
import { Building } from '../src/modules/communities/types/building'

loadEnv()

const getData = async (): Promise<Array<Partial<Building>>> => {
  const [community1, community2] = await Promise.all(
    ['Community Org A', 'Community Org B'].map(async (name) => {
      return await CommunityModel.findOne({ name })
    })
  )

  return [
    {
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        postalCode: faker.location.zipCode(),
        state: faker.location.state()
      },
      communityId: community1?._id
    },
    {
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        postalCode: faker.location.zipCode(),
        state: faker.location.state()
      },
      communityId: community1?._id
    },
    {
      address: {
        street: faker.location.streetAddress(),
        city: faker.location.city(),
        postalCode: faker.location.zipCode(),
        state: faker.location.state()
      },
      communityId: community2?._id
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()
    const promises = await data.map(async (item) => {
      await BuildingModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Buildings seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
