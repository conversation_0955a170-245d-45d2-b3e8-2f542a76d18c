import { loadEnv } from '../src/core/loadEnv'
import { RoleModel } from '../src/modules/users/models/role'

loadEnv()

const getData = async () => {
  return [
    {
      _id: '5ced1a18ff3085e86a0f2093',
      name: 'Admin',
      alias: 'admin',
      description: 'admin role'
    },
    {
      _id: '5f351eba20f4cb2d7ff24150',
      name: 'Organization admin',
      alias: 'organizationAdmin',
      description: 'organization admin role'
    },
    {
      _id: '5ced1a18ff3085e86a0f2094',
      name: 'Agent',
      alias: 'agent',
      description: 'communit admin role'
    },
    {
      _id: '5ced1a18ff3085e86a0f2096',
      name: 'Consumer',
      alias: 'consumer',
      description: 'consumer role'
    },
    {
      _id: '5ced1a18ff3085e86a0f2095',
      name: 'Scanner',
      alias: 'scanner',
      description: 'scanner role'
    },
    {
      _id: '5ced1a18ff3085e86a0f2097',
      name: 'Analytics',
      alias: 'analytics',
      description: 'analytics role'
    },
    {
      _id: '65b7babb8f3c7b4bc7710677',
      name: 'Owner',
      alias: 'owner',
      description: 'owner role'
    },
    {
      _id: '65b7babb8f3c7b4bc7710688',
      name: 'Basic',
      alias: 'basic',
      description: 'basic role'
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()
    const promises = data.map(async (item) => {
      const existing = await RoleModel.findOne({ alias: item.alias }).lean()

      return existing
        ? await RoleModel.updateOne({ _id: existing._id }, item)
        : await RoleModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Roles seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
