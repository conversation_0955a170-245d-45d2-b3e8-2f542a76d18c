import { loadEnv } from '../src/core/loadEnv'
import { ProspectModel } from '../src/modules/prospects/models/prospect'
import {
  Prospect,
  ProspectOrigins
} from '../src/modules/prospects/types/prospect'
import { CommunityModel } from '../src/modules/communities/models/community'
import { Community } from '../src/modules/communities/types/community'

loadEnv()

const getData = async (): Promise<Partial<Prospect>[]> => {
  const community = (await CommunityModel.findOne({}).lean()) as Community
  return [
    {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '1234567890',
      communityId: community._id.toString(),
      organizationId: community.organization?._id.toString(),
      origin: ProspectOrigins.Dashboard
    },
    {
      firstName: 'Organization',
      lastName: 'A User',
      email: '<EMAIL>',
      phone: '1234567890',
      communityId: community._id.toString(),
      organizationId: community.organization?._id.toString(),
      origin: ProspectOrigins.Dashboard
    },
    {
      firstName: 'Organization',
      lastName: 'B User',
      email: '<EMAIL>',
      phone: '1234567890',
      communityId: community._id.toString(),
      organizationId: community.organization?._id.toString(),
      origin: ProspectOrigins.Dashboard
    },
    {
      firstName: 'Consumer',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '1234567890',
      communityId: community._id.toString(),
      organizationId: community.organization?._id.toString(),
      origin: ProspectOrigins.Dashboard
    },
    {
      firstName: 'Bob',
      lastName: 'Esponje',
      email: '<EMAIL>',
      phone: '1234567890',
      communityId: community._id.toString(),
      organizationId: community.organization?._id.toString(),
      origin: ProspectOrigins.Dashboard
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()
    const promises = await data.map(async (item) => {
      const existing = await ProspectModel.findOne({ email: item.email }).lean()

      return existing
        ? await ProspectModel.updateOne({ _id: existing?._id }, item)
        : await ProspectModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Prospects seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
