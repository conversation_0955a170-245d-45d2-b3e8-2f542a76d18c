import { loadEnv } from '../src/core/loadEnv'
import { UserModel } from '../src/modules/users/models/user'
import { OrganizationModel } from '../src/modules/users/models/organization'
import { PolicyModel } from '../src/modules/users/models/policy'
import { ActionModel } from '../src/modules/users/models/action'
import { RoleModel } from '../src/modules/users/models/role'

loadEnv()

const getData = async () => {
  const [adminRole, organizationRole, communityRole, analyticsRole] =
    await Promise.all(
      ['admin', 'organizationAdmin', 'agent', 'analytics'].map(async (alias) =>
        RoleModel.findOne({ alias })
      )
    )

  const [organizationAUser, organizationBUser] = await Promise.all(
    ['<EMAIL>', '<EMAIL>'].map(async (email) =>
      UserModel.findOne({ email })
    )
  )

  const [organizationA, organizationB] = await Promise.all(
    ['Organization A', 'Organization B'].map(async (name) =>
      OrganizationModel.findOne({ name })
    )
  )

  const [
    fullAccess,
    readAccess,
    readOrganizations,
    manageOrganizations,
    readProperties,
    createProperties,
    createPageViews
  ] = await Promise.all(
    [
      'full',
      'read',
      'read_organizations',
      'manage_organizations',
      'create_organizations',
      'read_communities',
      'create_page_views'
    ].map(async (alias) => ActionModel.findOne({ alias }))
  )

  return [
    {
      resource: 'roles',
      roleId: adminRole?._id,
      allow: [fullAccess],
      deny: []
    },
    {
      resource: 'roles',
      roleId: organizationRole?._id,
      allow: [
        readAccess,
        readOrganizations,
        readProperties,
        manageOrganizations
      ],
      deny: []
    },
    {
      resource: 'roles',
      roleId: communityRole?._id,
      allow: [readAccess, readProperties],
      deny: []
    },
    {
      resource: 'roles',
      roleId: analyticsRole?._id,
      allow: [createPageViews],
      deny: []
    },
    {
      resource: 'organizations',
      organizationId: organizationA?._id,
      userId: organizationAUser?._id,
      allow: [createProperties],
      deny: []
    },
    {
      resource: 'organizations',
      organizationId: organizationB?._id,
      userId: organizationBUser?._id,
      allow: [createProperties],
      deny: []
    },
    {
      resource: 'organizations',
      organizationId: organizationA?._id,
      userId: organizationBUser?._id,
      allow: [],
      deny: [readOrganizations]
    },
    {
      resource: 'organizations',
      organizationId: organizationA?._id,
      userId: organizationBUser?._id,
      allow: [],
      deny: [readOrganizations]
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()

    await Promise.all(
      data.map(async (policy) => {
        if (policy.resource === 'roles') {
          const existing = await PolicyModel.findOne({
            resource: 'roles',
            roleId: policy.roleId
          }).lean()

          if (existing) {
            await PolicyModel.updateOne({ _id: existing._id }, policy)
          } else {
            await PolicyModel.create(policy)
          }
        } else if (policy.resource === 'organizations') {
          const existing = await PolicyModel.findOne({
            resource: 'organizations',
            organizationId: policy.organizationId,
            userId: policy.userId
          }).lean()

          if (existing) {
            await PolicyModel.updateOne({ _id: existing._id }, policy)
          } else {
            await PolicyModel.create(policy)
          }
        }
      })
    )
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Policies seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
