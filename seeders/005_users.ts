import { loadEnv } from '../src/core/loadEnv'
import { RoleModel } from '../src/modules/users/models/role'
import { UserModel } from '../src/modules/users/models/user'
import { encrypt } from '../src/core/encrypter'
import { User } from '../src/modules/users/types/user'
import { Role } from '../src/modules/users/types/role'
import { OrganizationModel } from '../src/modules/users/models/organization'
import { Organization } from '../src/modules/users/types/organization'
import { CommunityModel } from '../src/modules/communities/models/community'

loadEnv()

const getData = async (): Promise<Partial<User>[]> => {
  const roles = ['admin', 'organizationAdmin', 'consumer', 'agent', 'analytics']
  const [
    adminRole,
    organizationRole,
    consumerRole,
    communityRole,
    analyticsRole
  ]: Role[] = await Promise.all(
    roles.map(async (alias) => RoleModel.findOne({ alias }).lean())
  )

  const [organizationA, organizationB]: Array<Organization | null> =
    await Promise.all(
      ['Organization A', 'Organization B'].map(async (name) =>
        OrganizationModel.findOne({ name })
      )
    )

  const [communityOrgA, communityOrgA2, communityOrgB] = await Promise.all(
    ['Community Org A', 'Community Org A2', 'Community Org B'].map(
      async (name) => CommunityModel.findOne({ name })
    )
  )

  return [
    {
      name: 'Admin User',
      email: '<EMAIL>',
      roleId: adminRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890',
      organization: {
        _id: organizationA?._id,
        name: `${organizationA?.name}`
      },
      communities: []
    },
    {
      name: 'Organization A User',
      email: '<EMAIL>',
      roleId: organizationRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890',
      organization: {
        _id: organizationA?._id,
        name: `${organizationA?.name}`
      },
      communities: [
        {
          _id: communityOrgA?._id,
          name: communityOrgA?.name
        },
        {
          _id: communityOrgA2?._id,
          name: communityOrgA2?.name
        }
      ]
    },
    {
      name: 'Organization B User',
      email: '<EMAIL>',
      roleId: organizationRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890',
      organization: {
        _id: organizationB?._id,
        name: `${organizationB?.name}`
      },
      communities: [
        {
          _id: communityOrgB?._id,
          name: communityOrgB?.name
        }
      ]
    },
    {
      name: 'Consumer User',
      email: '<EMAIL>',
      roleId: consumerRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890'
    },
    {
      name: 'Community User',
      email: '<EMAIL>',
      roleId: communityRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890'
    },
    {
      name: 'Analytics Client',
      email: '<EMAIL>',
      roleId: analyticsRole?._id,
      password: await encrypt('Test1234!'),
      status: 'active',
      phone: '1234567890'
    }
  ]
}

const run = async () => {
  try {
    const data = await getData()
    const promises = data.map(async (item) => {
      const existing = await UserModel.findOne({ email: item.email }).lean()

      return existing
        ? await UserModel.updateOne({ _id: existing?._id }, item)
        : await UserModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Users seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
