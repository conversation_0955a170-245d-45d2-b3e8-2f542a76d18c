import { loadEnv } from '../src/core/loadEnv'
import { OrganizationModel } from '../src/modules/users/models/organization'

loadEnv()

const data = [
  {
    name: 'Peek',
    domains: ['peek.us']
  },
  {
    name: 'Organization A',
    domains: ['org.com']
  },
  {
    name: 'Organization B',
    domains: []
  },
  {
    name: 'Organization C',
    domains: []
  }
]

const run = async () => {
  try {
    const promises = data.map(async (item) => {
      const existing = await OrganizationModel.findOne({
        name: item.name
      }).lean()

      return existing
        ? await OrganizationModel.updateOne({ _id: existing._id }, item)
        : await OrganizationModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.log(err)
  } finally {
    console.log('Organizations seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
