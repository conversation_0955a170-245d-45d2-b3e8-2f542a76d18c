import { loadEnv } from '../src/core/loadEnv'
import { ActionModel } from '../src/modules/users/models/action'

loadEnv()

const data = [
  {
    name: 'Full Access',
    alias: 'full',
    description: 'Has full access to all resources'
  },
  {
    name: 'Read Access',
    alias: 'read',
    description: 'Has read access to all resources'
  },
  {
    name: 'Read users',
    alias: 'read_users',
    description: 'Can list users'
  },
  {
    name: 'Create Users',
    alias: 'create_users',
    description: 'Can read users'
  },
  {
    name: 'Read Organizations',
    alias: 'read_organizations',
    description: 'Can read organizations'
  },
  {
    name: 'Create Organizations',
    alias: 'create_organizations',
    description: 'Can read organizations'
  },
  {
    name: 'Manage Organizations',
    alias: 'manage_organizations',
    description: 'Can read organizations'
  },
  {
    name: 'Read Properties',
    alias: 'read_communities',
    description: 'Can read communities'
  },
  {
    name: 'Create Properties',
    alias: 'create_communities',
    description: 'Can create communities'
  },
  {
    name: 'Create Page Views',
    alias: 'create_page_views',
    description: 'Can create page views'
  }
]

const run = async () => {
  try {
    const promises = await data.map(async (item) => {
      const existing = await ActionModel.findOne({ alias: item.alias }).lean()

      return existing
        ? await ActionModel.updateOne({ _id: existing._id }, item)
        : await ActionModel.create(item)
    })
    await Promise.all(promises)
  } catch (err) {
    console.error(err)
  } finally {
    console.log('Actions seeded')
  }
}

if (process.env.EXEC_SEED == '1') {
  run()
}

export default run
