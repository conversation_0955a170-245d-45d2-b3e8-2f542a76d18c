/**
 * Serverless public that adds an environment variable telling sentry to capture error logs based on wether the lambda is triggered by a queue with reportBatchFailures set to true.
 */

//eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path')
// eslint-disable-next-line @typescript-eslint/no-var-requires
const fs = require('fs')

class SentryLogErrorPlugin {
  constructor(serverless) {
    this.serverless = serverless

    this.hooks = {
      initialize: this.validate.bind(this)
    }
  }

  async validate() {
    for (const functionName in this.serverless.service.functions) {
      const func = this.serverless.service.functions[functionName]
      if (func.events) {
        for (const event of func.events) {
          if (
            event.sqs &&
            event.sqs.functionResponseType === 'ReportBatchItemFailures'
          ) {
            func.environment = func.environment || {}
            func.environment.ENABLE_SENTRY_CONSOLE_ERROR = 'true'
          }
        }
      }
    }
  }
}

module.exports = SentryLogErrorPlugin
