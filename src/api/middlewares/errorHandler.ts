/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express'
import { logError, logInfo } from '@core/log'

const location = 'errorHandler'

const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const { params, query, body, method, headers } = req

  const errorInfo = {
    error: { ...error, stack: error.stack },
    params,
    query,
    body,
    method,
    headers
  }

  const status = error.status || 500
  if (status >= 500) {
    logError(location, error.message, errorInfo)
  } else {
    logInfo(location, error.message, errorInfo)
  }

  const payload =
    error.payload && Object.keys(error.payload).length ? error.payload : []
  res.status(status).json({ message: error.message, payload })
}

export default errorHandler
