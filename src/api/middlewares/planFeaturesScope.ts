import { PeekRequest } from '@api/interfaces'
import { getAclScopeQuery, setAclScopeQuery } from '@core/auth'
import { getFakeData } from '@core/fakeData'
import { getFeatureAccessCommunities } from '@modules/communities/repositories/community'
import { ObjectId } from '@modules/communities/types/id'
import { RoleAliases } from '@modules/users/types/role'
import { NextFunction, Response } from 'express'

/**
 * This middleware check access of plan features for communities. If any community has access then it returns
 * communities in request search else it returns fake data
 */
type PlanFeatureOptions = {
  filterKey?: string
  skipAclScope?: boolean
}

const planFeaturesScope = (
  feature: string,
  resource: string,
  options: PlanFeatureOptions = {}
) => {
  return async (
    req: PeekRequest,
    res: Response,
    next: NextFunction
  ): Promise<any> => {
    const { filterKey = 'communityId', skipAclScope = false } = options

    // For unauthorized routes it should never return fake data
    if (!req.authUser) {
      return next()
    }

    // When an admin user is logged in, we don't need to check for plan features
    if (req.authUser?.proxyUser?.roleAlias === RoleAliases.ADMIN) {
      return next()
    }

    // TODO: split daywise-spaces-views into two routes, one for pi and other for analytics
    // cause we want to enabled them based on feature gating
    if (
      req.baseUrl + req.path === '/analytics/daywise-spaces-views' &&
      !req.query.prospectId &&
      !req.query.groupBy
    ) {
      return next()
    }

    const currentQuery = getAclScopeQuery()
    let communitiesQuery = currentQuery[filterKey]

    if (
      req.params.scheduleEntityId &&
      ObjectId.isValid(req.params.scheduleEntityId)
    ) {
      communitiesQuery = { $in: [new ObjectId(req.params.scheduleEntityId)] }
    }

    //Get communitites with feature plan access
    const communities = await getFeatureAccessCommunities({
      _id: communitiesQuery,
      features: { $in: [feature] }
    })

    if (!communities.length) {
      const resourceFakeData = getFakeData(resource, req.query)
      return res.status(200).json(resourceFakeData)
    }

    if (!skipAclScope) {
      currentQuery[filterKey] = {
        $in: communities.map((item) => item._id)
      }
      setAclScopeQuery(currentQuery)
    }

    next()
  }
}
export default planFeaturesScope
