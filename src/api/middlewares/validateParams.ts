import { Request, Response, NextFunction } from 'express'
import { Schema } from 'joi'
import BadRequestError from '@core/errors/badRequestError'

const validateParams = (validator: Schema): any => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = validator.validate(req.params, { abortEarly: false })

    if (error) {
      throw new BadRequestError('Invalid request params')
    }

    next()
  }
}

export default validateParams
