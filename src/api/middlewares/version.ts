import { logInfo } from '@core/log'
import { Request, Response, NextFunction } from 'express'

const location = 'src/api/middlewares/version.ts'

export function versionMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const version = req.header('x-peek-api-version')
  const userAgent = req.headers['user-agent'] || ''
  const params = req.query
  const id = req.params?.id

  logInfo(
    location,
    `Version: ${
      version ?? 'default'
    }, User-Agent: ${userAgent}, Params: ${JSON.stringify(params)}, Id: ${id}`
  )

  if (version) {
    req['version'] = version as string
  }
  next()
}
