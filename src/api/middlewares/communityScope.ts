import { PeekRequest } from '@api/interfaces'
import {
  getAclFilter<PERSON>eys,
  getAclScopeQuery,
  setAclScopeQuery
} from '@core/auth'
import { CommunityModel } from '@modules/communities/models/community'
import { NextFunction, Response } from 'express'
import { ObjectId } from '@modules/communities/types/id'
import { AuthUser } from '@modules/users/types/user'

/**
 * This middleware adds query filter to the request object to search by community that the current user has access to
 */
const communityScope = (
  filterKey = 'communityId',
  options = { lookUpOrganization: false }
): any => {
  return async (
    req: PeekRequest,
    res: Response,
    next: NextFunction
  ): Promise<void | Response> => {
    if (!req.authUser) return next()

    const { lookUpOrganization = false } = options

    let communities = []

    const canSkipScope = !!req.authUser.allow.find(
      (action) =>
        action === `peek:roles:${req.authUser.roleId.toString()}::full`
    )

    if (canSkipScope) return next()

    let communityIdQS: string | string[] = <string | string[]>(
      req.query[filterKey]
    )

    let communityIdParam: string | string[] = <string | string[]>(
      req.params[filterKey]
    )

    const currentQuery = getAclScopeQuery()
    const currentFilterKeys = getAclFilterKeys()

    const organizationIdKey = currentFilterKeys['organizationIdKey']

    if (lookUpOrganization) {
      let organizationIds = []

      if (
        organizationIdKey &&
        currentQuery[organizationIdKey] &&
        currentQuery[organizationIdKey]['$in']
      ) {
        organizationIds = currentQuery[organizationIdKey]['$in'].map((id) =>
          id.toString()
        )
      } else {
        organizationIds = req.authUser.allow
          .filter((action) => action.includes('peek:organizations'))
          .map(
            (action) => action.replace('peek:organizations:', '').split('::')[0]
          )
      }

      const communitiesFromOrgs = await CommunityModel.find({
        'organization._id': { $in: organizationIds }
      })
        .setOptions({ skipAclScope: true })
        .select('_id')
        .lean()

      communities = communities.concat(
        communitiesFromOrgs.map((c) => c._id.toString())
      )

      if (communityIdQS) {
        communityIdQS =
          typeof communityIdQS === 'string' ? [communityIdQS] : communityIdQS

        communities = communities.filter((c) => communityIdQS.includes(c))
      }
    }

    const communitiesFromPolicies = req.authUser.allow
      .filter((action) => action.includes('peek:communities'))
      .map((action) => action.replace('peek:communities:', '').split('::')[0])

    if (communitiesFromPolicies?.length) {
      communities = lookUpOrganization
        ? communitiesFromPolicies.filter((c) => communities.includes(c))
        : communitiesFromPolicies
    }

    if (communityIdParam) {
      communityIdParam =
        typeof communityIdParam === 'string'
          ? [communityIdParam]
          : communityIdParam

      communities = communities.filter((c) => communityIdParam.includes(c))

      if (!communities.length) {
        return res.status(403).json({ message: 'Forbidden' })
      }
    }

    if (communityIdQS) {
      communities = communities.filter((c) => communityIdQS.includes(c))
    }

    if (!communities.length) return next()

    setAclScopeQuery({
      ...currentQuery,
      [filterKey]: { $in: communities.map((c) => new ObjectId(c)) }
    })

    next()
  }
}

export async function hasCommunityAccess(
  user: AuthUser,
  communityId: string,
  organizationId: string
): Promise<boolean> {
  const hasFullAccessRole = !!user.allow.find(
    (action) => action === `peek:roles:${user.roleId.toString()}::full`
  )

  const hasFullAccessOrganization = !!user.allow.find(
    (action) => action === `peek:organizations:${organizationId}::full`
  )

  const hasFullAccessCommunity = !!user.allow.find(
    (action) => action === `peek:communities:${communityId}::full`
  )

  if (hasFullAccessRole || hasFullAccessOrganization || hasFullAccessCommunity)
    return true

  let communities = user.allow
    .filter((action) => action.includes('peek:communities'))
    .map((action) => action.replace('peek:communities:', '').split('::')[0])

  const organizationIds = user.allow
    .filter((action) => action.includes('peek:organizations'))
    .map((action) => action.replace('peek:organizations:', '').split('::')[0])

  const communitiesFromOrgs = await CommunityModel.find({
    'organization._id': { $in: organizationIds }
  })
    .setOptions({ skipAclScope: true })
    .select('_id')
    .lean()

  communities = communities.concat(
    communitiesFromOrgs.map((c) => c._id.toString())
  )

  return communities.includes(communityId)
}

export default communityScope
