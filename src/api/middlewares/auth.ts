/* eslint-disable @typescript-eslint/ban-ts-comment */
import { PeekRequest } from '@api/interfaces'
import { setAuthUser, setCurrentToken } from '@core/auth'
import UnauthorizedError from '@core/errors/unauthorizedError'
import { logInfo } from '@core/log'
import { verify } from '@core/token'
import {
  extractPermissionsAndRestrictions,
  findPoliciesByRoleAndUserId
} from '@modules/users/services/policy'
import { AuthUser } from '@modules/users/types/user'
import { NextFunction, Response } from 'express'
import Sentry from '@sentry/node'

const location = 'middleware/auth'

type AuthenticateMiddlewareOptions = {
  anonymousAllowed?: boolean
}

const defaultOptions: AuthenticateMiddlewareOptions = {
  anonymousAllowed: false
}

const authenticate = (options = defaultOptions) => {
  return async (req: PeekRequest, res: Response, next: NextFunction) => {
    const token = req.headers.authorization?.split('Bearer ')[1]

    if (!token) {
      if (options.anonymousAllowed) {
        return next()
      }
      throw new UnauthorizedError('A token must be provided')
    }

    let user
    try {
      user = verify(token)
    } catch (error) {
      throw new UnauthorizedError('Token is invalid')
    }

    if (!user) {
      if (options.anonymousAllowed) {
        return next()
      }
      throw new UnauthorizedError('Token is invalid')
    }

    if (user?.status !== 'active') {
      if (options.anonymousAllowed) {
        return next()
      }
      throw new UnauthorizedError('User is not active')
    }

    const authUser: AuthUser = {
      ...user,
      allow: [],
      deny: []
    }

    const policies = await findPoliciesByRoleAndUserId(
      authUser.roleId.toString(),
      authUser._id.toString()
    )

    const { allow, deny } = extractPermissionsAndRestrictions(policies)

    authUser.allow = allow
    authUser.deny = deny

    if (authUser?.proxyUser) {
      logInfo(location, 'Request made by an admin on behalf of a user', {
        authUser: JSON.stringify(authUser),
        url: req.url,
        body: JSON.stringify(req.body),
        method: req.method
      })
    }

    req.authUser = authUser

    Sentry.setUser({
      id: authUser._id.toString(),
      allow: authUser.allow,
      deny: authUser.deny,
      proxyUser: {
        id: authUser.proxyUser?._id.toString()
      }
    })

    setAuthUser(authUser)
    setCurrentToken(token)

    next()
  }
}

export default authenticate
