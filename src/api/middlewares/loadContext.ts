import { ContextTypes } from '@api/interfaces'
import { connectDb } from '@core/db'
import { NextFunction, Response } from 'express'
import { logError, logInfo, logWarn } from '@core/log'
import { RequestContext } from '@modules/contexts/types'
import { findContextByContextId } from '@modules/contexts/repositories/context'
connectDb()

export interface ContextRequest<T extends ContextTypes = RequestContext> {
  params: { [key: string]: string }
  peekContext?: T
  path: string
}
const loadContextFromToken = async <T extends ContextTypes = RequestContext>(
  req: ContextRequest<T>,
  resp: Response,
  next: NextFunction
) => {
  if (req.params?.token) {
    const idx = req.params?.token.indexOf(':')
    /**
     * Make sure token has a context before looking up context
     */
    try {
      if (idx > 1) {
        const contextId = req.params?.token
        logInfo(`loadContext middleware ${req.path}`, 'Context Id found. ', {
          contextId,
          paramsToken: req.params.token
        })
        req.params.token = req.params?.token.substring(0, idx)
        const context = await findContextByContextId<T>(contextId)
        if (context) {
          logInfo(
            `loadContext middleware ${req.path}`,
            `${req.params.token} found a context`,
            { context }
          )
          req.peekContext = {
            contextId,
            ...context.context
          }
        } else {
          logWarn(`loadContext middleware ${req.path}`, 'No context found', {
            contextId,
            token: req.params.token
          })
        }
      }
    } catch (e) {
      /**
       * We should never fail hard in a middleware. The best thing would be for the
       * path to return if it is missing context it needs to operate.
       */
      logError(__filename, e.message)
    } finally {
      next()
    }
  }
}

export default loadContextFromToken
