import { Response, NextFunction } from 'express'
import ForbiddenError from '@core/errors/forbiddenError'
import { PeekRequest } from '@api/interfaces'

const checkPermission = (policies: string[]): any => {
  return (req: PeekRequest, res: Response, next: NextFunction): void => {
    const { authUser } = req

    const checkAction = (action: string, policies: string[]) => {
      return policies.find((item) => {
        const policy = item
          .replace(/<roleId>/g, authUser.roleId.toString())
          .replace(
            /<organizationId>/g,
            req.params.id ||
              req.query.organizationId ||
              req.body.organizationId ||
              req.body.organization?._id
          )
          .replace(
            /<communityId>/g,
            req.params.id ||
              req.query.communityId ||
              req.body.communityId ||
              req.body.community?._id
          )
        return policy === action
      })
    }

    const hasValidPolicy =
      !authUser.deny.find((action) => checkAction(action, policies)) &&
      authUser.allow.find((action) => checkAction(action, policies))

    if (authUser?.status !== 'active' || !hasValidPolicy) {
      throw new ForbiddenError('Action not allowed')
    }

    next()
  }
}

export default checkPermission
