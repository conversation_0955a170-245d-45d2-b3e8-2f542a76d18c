import { PeekRequest } from '@api/interfaces'
import {
  getAclFilter<PERSON>eys,
  getAclScopeQuery,
  setAclFilterKeys,
  setAclScopeQuery
} from '@core/auth'
import { NextFunction, Response } from 'express'
import { ObjectId } from '@modules/communities/types/id'

/**
 * This middleware adds query filter to the request object to search by organization that the current user has access to
 */
const organizationScope = (filterKey = 'organizationId'): any => {
  return (req: PeekRequest, res: Response, next: NextFunction): void => {
    if (!req.authUser) return next()

    const canSkipScope = !!req.authUser.allow.find(
      (action) =>
        action === `peek:roles:${req.authUser.roleId.toString()}::full`
    )

    if (canSkipScope) return next()

    let organizationIdQS: string | string[] = <string | string[]>(
      req.query[filterKey]
    )

    let organizations = req.authUser.allow
      .filter((action) => action.includes('peek:organizations'))
      .map((action) => action.replace('peek:organizations:', '').split('::')[0])

    if (organizationIdQS) {
      organizationIdQS =
        typeof organizationIdQS === 'string'
          ? [organizationIdQS]
          : organizationIdQS

      organizations = organizations.filter((c) => organizationIdQS.includes(c))
    }

    const currentQuery = getAclScopeQuery()
    const currentFilterKeys = getAclFilterKeys()

    setAclScopeQuery({
      ...currentQuery,
      [filterKey]: { $in: organizations.map((id) => new ObjectId(id)) }
    })

    setAclFilterKeys({
      ...currentFilterKeys,
      organizationIdKey: filterKey
    })

    next()
  }
}

export default organizationScope
