import { PeekRequest } from '@api/interfaces'
import BadRequestError from '@core/errors/badRequestError'
import { logError } from '@core/log'
import { NextFunction, Request, Response } from 'express'
import { Schema } from 'joi'
import { Zod<PERSON>rror, ZodSchema, infer as InferType } from 'zod'

type validateBodyProps = Schema | ((req: PeekRequest) => Schema)

const validateBody = (validator: validateBodyProps): any => {
  if (typeof validator === 'function') {
    return (req: PeekRequest, res: Response, next: NextFunction): void => {
      const { error } = validator(req).validate(req.body, { abortEarly: false })

      if (error) {
        throw new BadRequestError('Invalid request body', error.details)
      }

      next()
    }
  }
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = validator.validate(req.body, { abortEarly: false })

    if (error) {
      throw new BadRequestError('Invalid request body', error.details)
    }

    next()
  }
}

interface RequestWithBody<T> extends Request {
  body: T
}

export type ZodBodyRequest<T extends ZodSchema<any>> = RequestWithBody<
  InferType<T>
>

export function validateZodBody<T extends ZodSchema<any>>(schema: T) {
  return (
    req: RequestWithBody<InferType<T>>,
    res: Response,
    next: NextFunction
  ) => {
    try {
      req.body = schema.parse(req.body)
      next()
    } catch (error) {
      if (error instanceof ZodError) {
        throw new BadRequestError('Invalid request body', error.errors)
      } else {
        logError('validateBody', error)
        throw error
      }
    }
  }
}

export default validateBody
