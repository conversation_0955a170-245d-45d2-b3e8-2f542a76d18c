import { PeekRequest } from '@api/interfaces'
import ForbiddenError from '@core/errors/forbiddenError'
import { findPoliciesByRoleAndUserId } from '@modules/users/repositories/policy'
import { extractPermissionsAndRestrictions } from '@modules/users/services/policy'
import { findUserById } from '@modules/users/services/user'
import { NextFunction, Response } from 'express'

const proxyAcl = (neededPolicies: string[]): any => {
  return async (req: PeekRequest, res: Response, next: NextFunction) => {
    const { authUser } = req

    if (!authUser.proxyUser) {
      throw new ForbiddenError('Action not allowed')
    }

    const proxyUser = await findUserById(authUser.proxyUser._id.toString())

    if (proxyUser?.status !== 'active') {
      throw new ForbiddenError('Action not allowed')
    }

    const policies = await findPoliciesByRoleAndUserId(
      proxyUser.roleId.toString(),
      proxyUser._id.toString()
    )

    const checkAction = (action: string, policies: string[]) => {
      return policies.find((item) => {
        const policy = item
          .replace(/<roleId>/g, proxyUser.roleId.toString())
          .replace(
            /<organizationId>/g,
            req.params.id ||
              req.query.organizationId ||
              req.body.organizationId ||
              req.body.organization?._id
          )
          .replace(
            /<communityId>/g,
            req.params.id ||
              req.query.communityId ||
              req.body.communityId ||
              req.body.community?._id
          )
        return policy === action
      })
    }

    const { allow, deny } = extractPermissionsAndRestrictions(policies)

    const hasValidPolicy =
      !deny.find((action) => checkAction(action, neededPolicies)) &&
      allow.find((action) => checkAction(action, neededPolicies))

    if (!hasValidPolicy) {
      throw new ForbiddenError('Action not allowed')
    }

    next()
  }
}

export default proxyAcl
