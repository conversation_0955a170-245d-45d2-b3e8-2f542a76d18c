import { NextFunction, Request, Response } from 'express'
import httpContext from 'express-http-context'
import Sentry from '@sentry/node'

export function addTracingLog(req: Request, res: Response, next: NextFunction) {
  const { headers } = req
  const tracingId = headers['x-amzn-trace-id'] as string
  if (!tracingId) {
    return next()
  }

  if (!tracingId.includes('PeekSessionId=')) {
    return next()
  }

  const peekSessionId = tracingId.split('PeekSessionId=')[1].split(';')[0]

  if (!peekSessionId) {
    return next()
  }

  Sentry.setContext('Session', { id: peekSessionId })

  httpContext.set('sessionId', peekSessionId)

  next()
}
