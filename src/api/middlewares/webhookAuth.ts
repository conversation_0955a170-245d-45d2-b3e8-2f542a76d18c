import { NextFunction, Request, Response } from 'express'
import UnauthorizedError from '@core/errors/unauthorizedError'
import { getEnvVariable } from '@core/util'

const authenticateWebhook = (envVariableName?: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const apiKey = getEnvVariable(envVariableName)

    if (!apiKey) {
      throw new Error('Webhook API key not configured')
    }

    const authHeader = req.headers.authorization
    let providedToken: string | undefined

    if (authHeader?.startsWith('Bearer ')) {
      providedToken = authHeader.split('Bearer ')[1]
    }

    if (!providedToken) {
      providedToken = req.headers['x-api-key'] as string
    }

    if (!providedToken) {
      throw new UnauthorizedError(
        'API key is required. Provide it via Authorization header (Bearer token), X-API-Key, or X-Webhook-Key header.'
      )
    }

    if (providedToken !== apiKey) {
      throw new UnauthorizedError('Invalid API key')
    }

    next()
  }
}

export default authenticateWebhook
