import { Request, Response, NextFunction } from 'express'
import { Schema } from 'joi'
import BadRequestError from '@core/errors/badRequestError'
import { PeekRequest } from '@api/interfaces'
import { ZodError, ZodSchema } from 'zod'
import { logError } from '@core/log'

type validateQueryProps = Schema | ((req: PeekRequest) => Schema)

const validateQuery = (validator: validateQueryProps): any => {
  if (typeof validator === 'function') {
    return (req: PeekRequest, res: Response, next: NextFunction): void => {
      const { error } = validator(req).validate(req.query, {
        abortEarly: false
      })

      if (error) {
        throw new BadRequestError('Invalid request query', error.details)
      }

      next()
    }
  }
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = validator.validate(req.query, { abortEarly: false })

    if (error) {
      throw new BadRequestError('Invalid request query', error.details)
    }

    next()
  }
}

export function validateZodQuery<T extends ZodSchema<any>>(schema: T) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      req.query = schema.parse(req.query)
      next()
    } catch (error) {
      if (error instanceof ZodError) {
        throw new BadRequestError('Invalid request query', error.errors)
      } else {
        logError('validateQuery', error)
        throw error
      }
    }
  }
}

export default validateQuery
