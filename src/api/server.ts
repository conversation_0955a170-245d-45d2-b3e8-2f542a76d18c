import { loadEnv } from '@core/loadEnv'
loadEnv()

import { disconnectDb } from '@core/db'
import { logInfo } from '@core/log'
import { app } from './app'

const port = process.env.PORT || 4000
const location = 'server'

process
  .on('unhandledRejection', (reason, p) => {
    console.error(reason, 'Unhandled Rejection at Promise', p)
  })
  .on('uncaughtException', async (err) => {
    console.error(err, 'Uncaught Exception thrown')
    await disconnectDb()
    process.exit(1)
  })

export const server = app.listen(port, () => {
  logInfo(location, `Server listening @ http://localhost:${port}`)
})
