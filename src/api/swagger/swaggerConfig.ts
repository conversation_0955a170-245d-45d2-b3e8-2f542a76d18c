import { readdirSync } from 'fs'
import path from 'path'
import { swaggerDoc } from './swagger'
import j2s from 'joi-to-swagger'
import { Schema } from 'joi'
import { logError } from '@core/log'

const getModules = (source: string) =>
  readdirSync(source, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name)

const generatePaths = async (source: string) => {
  const moduleFolders = readdirSync(source, { withFileTypes: true })
  const pathsFolder = moduleFolders.find(
    (dirent) => dirent.isDirectory() && dirent.name === 'paths'
  )
  let paths = {}
  if (pathsFolder) {
    const files = readdirSync(`${source}/${pathsFolder.name}`, {
      withFileTypes: true
    })
    for (const file of files) {
      if (file.isFile()) {
        const pathFile = await import(
          `${source}/${pathsFolder.name}/${file.name}`
        )
        paths = {
          ...paths,
          ...pathFile.default
        }
      }
    }
  }
  return paths
}

export const generateSchemas = async (source: string) => {
  const schemaFolders = readdirSync(source, { withFileTypes: true })
  const schemasFolder = schemaFolders.find(
    (dirent) => dirent.isDirectory() && dirent.name === 'schemas'
  )

  const schemas = {}

  if (schemasFolder) {
    const files = readdirSync(`${source}/${schemasFolder.name}`, {
      withFileTypes: true
    })
    for (const file of files) {
      if (file.isFile()) {
        const schemaFile = await import(
          `${source}/${schemasFolder.name}/${file.name}`
        )
        for (const [key, value] of Object.entries(schemaFile)) {
          if (value) {
            try {
              const { swagger: component } = j2s(value as Schema<any>)
              schemas[key] = component
            } catch (error) {
              if (error instanceof TypeError) {
                continue
              }
              logError('swaggerConfig', error)
            }
          }
        }
      }
    }
  }
  return schemas
}

const findSwaggerConfig = (source: string) => {
  const moduleFolders = readdirSync(source, { withFileTypes: true })
  const swaggerFolder = moduleFolders.find(
    (dirent) => dirent.isDirectory() && dirent.name === 'swagger'
  )
  if (swaggerFolder) {
    const files = readdirSync(`${source}/${swaggerFolder.name}`, {
      withFileTypes: true
    })
    return files.find(
      (dirent) => dirent.isFile() && dirent.name === 'swagger.ts'
    )
  }
}

export const generateSwagger = async () => {
  const modules = getModules(path.join(__dirname, '../../modules'))
  const moduleSwaggerDocs = []
  for (const module of modules) {
    const paths = await generatePaths(
      path.join(__dirname, `../../modules/${module}`)
    )
    const schemas = await generateSchemas(
      path.join(__dirname, `../../modules/${module}`)
    )

    const moduleSwagger = findSwaggerConfig(
      path.join(__dirname, `../../modules/${module}`)
    )
    if (moduleSwagger) {
      const moduleSwaggerFile = await import(
        `${path.join(__dirname, `../../modules/${module}/swagger`)}/${
          moduleSwagger.name
        }`
      )
      moduleSwaggerFile.swaggerDoc.paths = paths
      moduleSwaggerFile.swaggerDoc.components.schemas = schemas
      moduleSwaggerDocs.push(moduleSwaggerFile.swaggerDoc)
    } else {
      swaggerDoc.paths = {
        ...swaggerDoc.paths,
        ...paths
      }
      swaggerDoc.components.schemas = {
        ...swaggerDoc.components.schemas,
        ...schemas
      }
    }
  }
  return [swaggerDoc, ...moduleSwaggerDocs]
}
