export const swaggerDoc = {
  openapi: '3.0.3',
  info: {
    title: 'Peek Api V3',
    route: '/api',
    description: 'This is the documentation for our peek api v3',
    termsOfService: 'http://swagger.io/terms/',
    contact: {
      email: '<EMAIL>'
    },
    license: {
      name: 'Apache 2.0',
      url: 'http://www.apache.org/licenses/LICENSE-2.0.html'
    },
    version: '1.0.0'
  },
  servers: [
    {
      url: process.env.BASE_URL
    }
  ],
  tags: [
    {
      name: 'auth',
      description: 'Authenticate in our api',
      externalDocs: {
        description: 'Find out more',
        url: 'https://swagger.io/docs/specification/authentication/bearer-authentication/'
      }
    },
    {
      name: 'users',
      description: 'Users'
    },
    {
      name: 'organizations',
      description: 'Organizations'
    },
    {
      name: 'roles',
      description: 'Roles'
    }
  ],
  paths: {},
  components: {
    schemas: {},
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT'
      }
    }
  }
}
