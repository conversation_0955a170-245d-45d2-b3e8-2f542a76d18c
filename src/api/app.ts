import { app } from '@core/appConfig'

import * as Sentry from '@sentry/node'
import errorHandler from '@api/middlewares/errorHandler'
import cookies from 'cookie-parser'
import express from 'express'
import 'express-async-errors'
import httpContext from 'express-http-context'
import routes from './routes'
import { trackingPixel } from './routes/trackingPixel'
import { addTracingLog } from './middlewares/tracing'

app.use(express.json({ limit: '50mb' }))
app.use(express.urlencoded({ extended: true, limit: '50mb' }))

app.use(cookies())

app.use(httpContext.middleware)
app.use(addTracingLog)
app.use(routes)

Sentry.setupExpressErrorHandler(app)

app.use(errorHandler)

app.get('/:trackingId.gif', trackingPixel)

export { app }
