import { Request } from 'express'
import { AuthUser } from '@modules/users/types/user'
import {
  RequestContext,
  VirtualTourLinkRequestContext
} from '@modules/contexts/types'

export type ContextTypes = RequestContext | VirtualTourLinkRequestContext
export interface PeekRequest<T extends ContextTypes = RequestContext>
  extends Request {
  authUser: AuthUser
  version?: string | undefined
  peekContext?: T
}
