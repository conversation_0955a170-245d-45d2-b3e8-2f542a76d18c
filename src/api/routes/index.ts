import { Request, Response, Router } from 'express'
import userModuleRoutes from '@modules/users/routes'
import prospectModuleRoutes from '@modules/prospects/routes'
import communityModuleRoutes from '@modules/communities/routes'
import analyticsModuleRoutes from '@modules/analytics/routes'
import swaggerUi from 'swagger-ui-express'
import { generateSwagger } from '@api/swagger/swaggerConfig'
import uploadModuleRoutes from '@modules/upload/routes'
import sgtModuleRoutes from '@modules/sgt/routes'
import mongoose from 'mongoose'
import { logError } from '@core/log'
import magicButtonRoutes from '@modules/magicButtons/routes'
import { GetCallerIdentityCommand, STSClient } from '@aws-sdk/client-sts'

const router = Router()

generateSwagger().then((swaggerDocs) => {
  swaggerDocs.forEach((swaggerDoc) => {
    const swaggerDocument = JSON.parse(JSON.stringify(swaggerDoc))
    router.use(
      `/docs${swaggerDocument.info.route}`,
      swaggerUi.serveFiles(swaggerDocument) as any,
      swaggerUi.setup(swaggerDocument) as any
    )
  })
})

router.get('/', async (req: Request, res: Response) => {
  const CONNECTED_STATE = 1
  const dbConnectionStatus = mongoose.connection.readyState
  if (dbConnectionStatus !== CONNECTED_STATE) {
    return res.status(503).send('Service Unavailable')
  }

  try {
    const stsClient = new STSClient({})
    const command = new GetCallerIdentityCommand()
    await stsClient.send(command)

    await mongoose.connection.db.listCollections().toArray()
    return res.status(200).send('Peek API')
  } catch (error) {
    logError('healthCheck', 'Error sending 200 code to health-check', error)
    return res.status(503).send('Service Unavailable')
  }
})

router.use(userModuleRoutes)
router.use(communityModuleRoutes)
router.use(prospectModuleRoutes)
router.use(analyticsModuleRoutes)
router.use(uploadModuleRoutes)
router.use(sgtModuleRoutes)
router.use(magicButtonRoutes)

export default router
