import { logError, logWarn } from '@core/log'
import { logInfo } from '@core/log'
import { ProspectModel } from '@modules/prospects/models/prospect'
import { Prospect } from '@modules/prospects/types/prospect'
import axios from 'axios'
import { Request, Response } from 'express'

const location = 'trackingPixel'

/**
 * @param obj
 * @return {obj is Object}
 */
export function isObject(obj) {
  if (!(obj && (typeof obj === 'object' || obj !== null))) return false

  let proto = obj
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto)
  }

  return Object.getPrototypeOf(obj) === proto
}

/**
 * Safe JSON parse
 * @param  {*} input - value to parse
 * @return {*} parsed input
 */
export function parse(input: any) {
  let value = input
  try {
    value = JSON.parse(input)
    if (value === 'true') return true
    if (value === 'false') return false
    if (isObject(value)) return value
    if (parseFloat(value) === value) {
      value = parseFloat(value)
    }
  } catch (e) {
    logWarn(location, `Couldn't parse JSON ${input}`, e)
  }
  if (value === null || value === '') {
    return
  }
  return value
}

export function uuid() {
  const m = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'
  let u = '',
    i = 0,
    rb = (Math.random() * 0xffffffff) | 0

  while (i++ < 36) {
    const c = m[i - 1],
      r = rb & 0xf,
      v = c == 'x' ? r : (r & 0x3) | 0x8

    u += c == '-' || c == '4' ? c : v.toString(16)
    rb = i % 8 == 0 ? (Math.random() * 0xffffffff) | 0 : rb >> 4
  }
  return u
}

const COOKIE_MAP = {
  anonymousId: '__anon_id',
  userId: '__user_id',
  communityCustomData: '__community_custom_data'
}

async function sendIdentifyWebEvent(
  anonymousId: string,
  prospect: Prospect,
  req: Request
) {
  try {
    const result = await axios.put(process.env.ANALYTICS_URL, {
      type: 'identify',
      traits: {
        prospectEmail: prospect.email,
        prospectCommunity: prospect.communityId
      },
      anonymousId: anonymousId.replace(/-/g, ''),
      secondsUtcTS: new Date().getTime(),
      referrer: req.headers.origin,
      userAgent: req.headers['user-agent'],

      appId: 'contact-script',
      app: 'contact-script',
      prospectId: prospect._id
    })
    logInfo(location, 'Identify event sent to analytics', result.data)
  } catch (error) {
    logError(location, 'Error sending identify event to analytics', error)
  }
}

export async function trackingPixel(req: Request, res: Response) {
  logInfo(location, 'Tracking pixel hit')
  const trackingId = req.params.trackingId

  if (trackingId === 'community_analytics_custom_data') {
    communityDataTrackingPixel(req, res)
  } else {
    await prospectTrackingPixel(req, res)
  }
  // Send 1x1 transparent pixel
  const pixel = Buffer.from(
    'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
    'base64'
  )
  res.set('Content-Type', 'image/gif')
  res.set('Cross-Origin-Resource-Policy', 'cross-origin')
  res.send(pixel)
}

function communityDataTrackingPixel(req: Request, res: Response) {
  if (req.query) {
    res.cookie(COOKIE_MAP.communityCustomData, JSON.stringify(req.query), {
      domain: '.peek.us',
      sameSite: 'none',
      secure: true,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365) // 1 year
    })
  }
}

async function prospectTrackingPixel(req: Request, res: Response) {
  const anonId = req.cookies[COOKIE_MAP.anonymousId] || JSON.stringify(uuid())

  const prospect = await ProspectModel.findById(req.params.trackingId)

  if (!prospect) {
    return res.status(404).send('Not found')
  }

  // NOTE: We get stringify cookie value from the client, so we need to parse it
  // otherwise PUT request to analytics will fail
  await sendIdentifyWebEvent(parse(anonId), prospect, req)

  res.cookie(COOKIE_MAP.anonymousId, anonId, {
    domain: '.peek.us',
    sameSite: 'none',
    secure: true,
    expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365) // 1 year
  })
  res.cookie(COOKIE_MAP.userId, req.params.trackingId?.toString(), {
    domain: '.peek.us',
    sameSite: 'none',
    secure: true,
    expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365) // 1 year
  })
}
