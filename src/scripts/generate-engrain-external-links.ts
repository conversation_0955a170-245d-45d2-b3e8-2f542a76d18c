import { connectDb } from '@core/db'
import { findAllDxSettings } from '@modules/communities/repositories/dxSetting'
import { DXSettingServices } from '@modules/communities/types/dxSetting'
import { getOutboundLinks, getUnit } from '@modules/engrain/gateways/engrain'
import { OutboundLinkData, UnitResponse } from '@modules/engrain/types/engrain'
import {
  createExternalLink,
  findExternalLinkBy
} from '@modules/communities/repositories/externalLink'
import {
  ExternalLinkCollection,
  ExternalLinkName,
  ExternalLinkService
} from '@modules/communities/types/externalLink'
import { findAllSpaces } from '@modules/communities/repositories/space'
import { sleep } from '@core/util'

async function main() {
  await connectDb()

  const dxSettings = await findAllDxSettings({
    service: DXSettingServices.ENGRAIN,
    'dataExchange.syncIls': true,
    deletedAt: null
  })

  for (const dxSetting of dxSettings) {
    let outboundLinkResponse

    try {
      outboundLinkResponse = await getOutboundLinks({
        dxSetting
      })
    } catch (err) {
      console.error('Error getting outbound links', dxSetting._id.toString())
      continue
    }

    if (outboundLinkResponse.data.length === 0) {
      console.log(`No outbound links found for ${dxSetting.engrain.assetId}`)
      continue
    }

    const outboundLink = outboundLinkResponse.data.find(
      (link: OutboundLinkData) =>
        link.label === dxSetting.engrain.label &&
        link.name === dxSetting.engrain.name
    )

    if (!outboundLink) {
      console.log(
        `No outbound link found for ${dxSetting.engrain.assetId} with label ${dxSetting.engrain.label} and name ${dxSetting.engrain.name}`
      )
      continue
    }

    console.log('Outbound link found', outboundLink.id)
    const outboundLinkExternalLink = await findExternalLinkBy({
      service: ExternalLinkService.Engrain,
      objectId: dxSetting.communityId,
      communityId: dxSetting.communityId,
      externalName: ExternalLinkName.OutboundLink,
      deletedAt: null
    })

    if (!outboundLinkExternalLink) {
      await createExternalLink({
        communityId: dxSetting.communityId,
        service: ExternalLinkService.Engrain,
        externalName: ExternalLinkName.OutboundLink,
        externalId: outboundLink.id,
        collectionPath: ExternalLinkCollection.OutboundLink,
        objectId: dxSetting.communityId
      })
      console.log('Created outbound link', outboundLink.id)
    }

    const spaces = await findAllSpaces({
      'community._id': dxSetting.communityId,
      deletedAt: null
    })

    console.log(
      `Found ${spaces.length} spaces for ${dxSetting.engrain.assetId}`
    )

    await Promise.all(
      spaces.map(async (space) => {
        const engrainUnitResponse = await forceGetUnitRequest({
          dxSetting,
          unit: space.unit
        })
        if (!engrainUnitResponse?.data?.length) {
          console.log(`No engrain unit found for ${space.unit}`)
          return
        }
        console.log(`Engrain unit: ${engrainUnitResponse.data[0].id}`)

        const unitExternalLink = await findExternalLinkBy({
          communityId: dxSetting.communityId,
          objectId: space._id,
          service: ExternalLinkService.Engrain,
          externalName: ExternalLinkName.EngrainUnit,
          collectionPath: ExternalLinkCollection.Spaces,
          deletedAt: null
        })

        if (!unitExternalLink) {
          await createExternalLink({
            communityId: dxSetting.communityId,
            objectId: space._id.toString(),
            service: ExternalLinkService.Engrain,
            externalName: ExternalLinkName.EngrainUnit,
            externalId: engrainUnitResponse.data[0].id,
            collectionPath: ExternalLinkCollection.Spaces
          })
        }
      })
    )
  }
}

const forceGetUnitRequest = async ({
  dxSetting,
  unit,
  count = 0,
  time = 0
}) => {
  await sleep(time)

  let engrainUnitResponse: UnitResponse

  try {
    engrainUnitResponse = await getUnit({ dxSetting, unit })
  } catch (err) {
    if (err.response.statusText === 'Too Many Requests') {
      count += 1
      time += 2000
      console.log(`rate limit exceeded for ${unit}, retrying... (${count})`)
      return await forceGetUnitRequest({
        dxSetting,
        unit,
        count,
        time
      })
    }
    return null
  }
  return engrainUnitResponse
}

main()
  .then(() => {
    console.log('Done')
    process.exit(0)
  })
  .catch((err) => {
    console.error(err)
    process.exit(1)
  })
