import { CommunityModel } from '../modules/communities/models/community'
import { connectDb } from '../core/db'
import { loadEnv } from '../core/loadEnv'

async function restoreToursField() {
  try {
    loadEnv()
    await connectDb()

    const communityId = process.argv[2]

    const communities = communityId
      ? [await CommunityModel.findById(communityId)].filter(Boolean)
      : await CommunityModel.find({})

    let updated = 0

    for (const community of communities) {
      const hasSGT = community.features?.includes('SGT')

      const tours = {
        AGT: { isAvailable: !hasSGT },
        SGT: { isAvailable: !!hasSGT }
      }

      await CommunityModel.updateOne(
        { _id: community._id },
        { $set: { tours } }
      )

      console.log(
        `Updated community ${community.name} (${community._id}) with:`,
        tours
      )
      updated++
    }

    console.log(`Restored 'tours' field in ${updated} community(ies)`)
  } catch (error) {
    console.error('Error restoring tours field:', error)
  } finally {
    process.exit()
  }
}

restoreToursField()
