class NotFoundError extends Error {
  status: number
  payload: any

  static isInstance(error: any): error is NotFoundError {
    return error.status === 404
  }

  constructor(message: string, payload: any = {}) {
    super(message)
    Error.captureStackTrace(this, this.constructor)

    this.name = this.constructor.name
    this.status = 404
    this.payload = payload
  }
}

export default NotFoundError
