import { Unit, FloorPlan, Property, Price } from '../types/cortlandTypes'
import {
  AvailableDatesMetadata,
  ExternalSpaceStatus,
  RentPrice,
  Space
} from '@modules/communities/types/space'
import {
  ExternalLinkName,
  ExternalLinkService
} from '@modules/communities/types/externalLink'
import { SpaceType } from '@modules/communities/types/spaceType'
import { Address } from '@modules/communities/types/address'

import { DXSetting } from '@modules/communities/types/dxSetting'
import { SpaceEventBody } from '@modules/dataExchange/types/dataExchange'
import { parse } from 'date-fns'

export const buildSpaceEventBody = (
  unit: Unit,
  prices: Price[],
  floorplan: FloorPlan,
  property: Property,
  dxSetting: DXSetting
): SpaceEventBody => {
  return {
    setting: {
      dxSettingId: dxSetting._id.toString()
    },
    community: {
      _id: dxSetting.communityId.toString()
    },
    space: buildSpaceBody(unit, floorplan, prices),
    address: buildAddressBody(property),
    external: {
      externalLinkService: ExternalLinkService.Cortland,
      spaces: [
        {
          externalId: unit.id.toString(),
          externalName: ExternalLinkName.UnitDotId,
          service: ExternalLinkService.Cortland
        }
      ]
    }
  }
}

export const buildAddressBody = (property: Property): Address => {
  const address: Address = {
    city: property.city,
    state: property.state,
    postalCode: property.zip,
    street: property.address
  }

  return address
}

const buildSpaceBody = (
  unit: Unit,
  floorplan: FloorPlan,
  prices: Price[]
): Partial<Space> => {
  const space: Partial<Space> = {
    bathrooms: floorplan.bathrooms,
    bedrooms: floorplan.bedrooms,
    externalSpaceStatus: [mapExternalStatus(unit.isAvailable)],
    availabilityStatus: unit.isAvailable ? 'Available' : 'Unavailable',
    availableStatusesMetadata: buildAvailableStatusesMetadata(unit),
    availableDatesMetadata: buildAvailableDatesMetadata(unit.availableOnDt),
    isMarketable: unit.isAvailable,
    floorPlan: {
      name: floorplan.name,
      externalId: floorplan.id.toString()
    },
    unit: unit.unitNumber,
    type: SpaceType.Unit,
    unitSize: floorplan.sqFt,
    rentPrices: unit.isAvailable ? buildRentPrices(prices) : [],
    isVisible: unit.isAvailable,
    pricesMetadata: buildPricesMetadata(unit)
  }

  return space
}

const buildPricesMetadata = (unit: Unit) => {
  return {
    minRent: unit.minRent,
    maxRent: unit.maxRent
  }
}

const buildRentPrices = (prices: Price[]): RentPrice[] => {
  const filteredUnitPrices = prices.reduce((acc, price) => {
    const existingPrice = acc.find((p) => p.term === price.term)
    if (!existingPrice || price.price < existingPrice.price) {
      const filtered = acc.filter((p) => p.term !== price.term)
      return [...filtered, price]
    }
    return acc
  }, [] as typeof prices)
  const sortedUnitPrices = filteredUnitPrices.sort((a, b) => a.term - b.term)

  return sortedUnitPrices.map((price) => ({
    enabled: true,
    price: price.price,
    termInMonths: price.term
  }))
}

const buildAvailableStatusesMetadata = (unit: Unit) => {
  return {
    isAvailable: unit.isAvailable,
    isVacant: unit.isVacant,
    buildingNumber: unit.buildingNumber
  }
}

const buildAvailableDatesMetadata = (
  availableOnDt: string | undefined
): AvailableDatesMetadata | undefined => {
  if (!availableOnDt) return undefined

  const parsedDate = parse(
    availableOnDt,
    "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
    new Date()
  )

  return {
    availableOnDt: parsedDate
  }
}

const mapExternalStatus = (isAvailable: boolean) => {
  return isAvailable
    ? ExternalSpaceStatus.Available
    : ExternalSpaceStatus.Unavailable
}
