import axios from 'axios'
import { logDebug, logError } from '@core/log'
import {
  GetPropertyByIdResponse,
  GraphQLResponse,
  GraphQLRequest,
  Property,
  GetPricesByCriteriaResponse,
  GetPricesByCriteriaFilters
} from '../types/cortlandTypes'
import { getEnvVariable } from '@core/util'

const location = 'cortland-gateway'

export const callCortlandGraphQL = async <T>(
  request: GraphQLRequest
): Promise<T> => {
  const apiUrl = getEnvVariable('CORTLAND_API_URL')
  const apiKey = getEnvVariable('CORTLAND_API_KEY')

  try {
    logDebug(location, 'Making GraphQL request to Cortland API', {
      url: apiUrl,
      query: request.query,
      variables: request.variables
    })

    const response = await axios.post<GraphQLResponse<T>>(apiUrl, request, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': api<PERSON>ey
      }
    })

    if (response.data.errors && response.data.errors.length > 0) {
      const error = new Error(
        `GraphQL Error: ${response.data.errors[0].message}`
      )
      logError(location, 'GraphQL request failed', {
        errors: response.data.errors
      })
      throw error
    }

    return response.data.data
  } catch (error) {
    logError(location, 'Error calling Cortland GraphQL API', { error })
    throw error
  }
}

export const fetchCortlandProperty = async (id: number): Promise<Property> => {
  const query = `
    query GetPropertyById {
      getPropertyById(id: ${id}) {
        leaseTermsUpdateDT
        propertyUpdateDT
        availabilityUpdateDT
        pricingUpdateDT
        id
        name
        phone
        email
        city
        state
        zip
        address
        leaseTerms {
            new
            renewal
            transfer
        }
        floorPlans {
            id
            name
            bedrooms
            bathrooms
            sqFt
            minRent
            maxRent
            units {
                id
                unitNumber
                buildingId
                buildingNumber
                floorNumber
                isAvailable
                isVacant
                availableOnDt
                minRent
                maxRent
            }
        }
      }
    }
  `

  const response = await callCortlandGraphQL<GetPropertyByIdResponse>({ query })
  return response.getPropertyById
}

export const fetchCortlandUnitPrices = async (
  filters: GetPricesByCriteriaFilters,
  page = 1,
  itemsPerPage = 10000
): Promise<GetPricesByCriteriaResponse['getPricesByCriteria']['data']> => {
  const query = `
    query GetPricesByCriteria {
      getPricesByCriteria(
        filters: [
          { value: ${filters.propertyId}, operator: "eq", field: "propertyId" }
          ${
            filters.unitId
              ? `{ value: ${filters.unitId}, operator: "eq", field: "unitId" }`
              : ''
          }
        ]
        orderBy: [
          { by: "leaseStartDt", type: "asc" }, 
          { by: "term", type: "asc" }
        ]
        page: ${page}
        itemsPerPage: ${itemsPerPage}
      ) {
        count
        data {
          id
          propertyId
          floorPlanId
          unitId
          unitNumber
          buildingId
          buildingNumber
          term
          leaseStartDt
          leaseEndDt
          price
          earlyOffer
          offerTerm
          generatedOnDT
          file
          createdAt
        }
      }
    }
  `

  const response = await callCortlandGraphQL<GetPricesByCriteriaResponse>({
    query
  })

  return response.getPricesByCriteria.data
}
