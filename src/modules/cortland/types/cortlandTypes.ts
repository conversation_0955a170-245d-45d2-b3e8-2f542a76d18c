export interface GraphQLResponse<T> {
  data: T
  errors?: Array<{
    message: string
    locations: Array<{
      line: number
      column: number
    }>
    path: string[]
  }>
}

export interface GraphQLRequest {
  query: string
  variables?: Record<string, any>
}

export interface GetPricesByCriteriaFilters {
  propertyId: number
  unitId?: number
}

export interface GetPricesByCriteriaResponse {
  getPricesByCriteria: {
    count: number
    data: Price[]
  }
}

export interface Price {
  id: number
  propertyId: number
  floorPlanId: number
  unitId: number
  unitNumber: string
  buildingId: number
  buildingNumber: string
  term: number
  leaseStartDt: string
  leaseEndDt: string
  price: number
  earlyOffer: boolean
  offerTerm: number
  generatedOnDT: string
  file: string
  createdAt: string
}

export interface Property {
  id: number
  name: string
  phone: string
  email: string
  city: string
  state: string
  zip: string
  address: string
  leaseTermsUpdateDT: string
  propertyUpdateDT: string
  availabilityUpdateDT: string
  pricingUpdateDT: string
  leaseTerms: LeaseTerms
  floorPlans: FloorPlan[]
}

export interface LeaseTerms {
  new: number
  renewal: number
  transfer: number
}

export interface FloorPlan {
  id: number
  name: string
  bedrooms: number
  bathrooms: number
  sqFt: number
  minRent: number
  maxRent: number
  units: Unit[]
}

export interface Unit {
  id: number
  unitNumber: string
  buildingId: string
  buildingNumber: string
  floorNumber: string
  isAvailable: boolean
  isVacant: boolean
  availableOnDt: string
  minRent: number
  maxRent: number
}

export interface GetPropertyByIdResponse {
  getPropertyById: Property
}
