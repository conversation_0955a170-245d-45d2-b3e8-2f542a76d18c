import NotFoundError from '@core/errors/notFoundError'
import { logDebug } from '@core/log'
import { removeUndef } from '@core/util'
import { generateSpaceToken } from '@modules/communities/services/space'
import { ExternalLinkService } from '@modules/communities/types/externalLink'
import { Space, SpaceWithExternalLinks } from '@modules/communities/types/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import * as contextRepo from '@modules/contexts/repositories/context'
import { VirtualTourLinkRequestContext } from '@modules/contexts/types'
import * as prospectsRepo from '@modules/prospects/repositories/prospect'
import { Prospect, ProspectOrigins } from '@modules/prospects/types/prospect'
import { nanoid } from 'nanoid'
import * as spacesRepo from '../../communities/repositories/space'
import { GetSpacesByCommunityIdParams } from '../types'

const { WEB_VIEWER_BASE_URL } = process.env

if (!WEB_VIEWER_BASE_URL) {
  throw new Error('WEB_VIEWER_BASE_URL is not defined')
}

export const getSpacesByCommunityId = async ({
  id,
  organizationId,
  type,
  availability,
  service,
  options
}: GetSpacesByCommunityIdParams) => {
  if (!options)
    options = {
      fields: {
        _id: 1,
        token: 1,
        type: 1,
        bedrooms: 1,
        bathrooms: 1,
        unit: 1,
        building: 1,
        isModelUnit: 1,
        layoutType: 1,
        floorPlan: 1,
        community: 1,
        isComplete: 1,
        isVisible: 1
      }
    }

  let spaces: Partial<Space>[]
  if (service) {
    spaces = await spacesRepo.findSpacesByCommunityExternalServiceId(
      id,
      organizationId,
      service,
      availability,
      type,
      options
    )
  } else {
    spaces = await spacesRepo.findSpacesWithExternalLinksByCommunityId(
      id,
      organizationId,
      availability,
      type,
      options
    )
  }

  if (!spaces.length) throw new NotFoundError('Community or spaces not found.')

  const partnerSpaces = []
  for (const space of spaces) {
    const partnerSpace = space as SpaceWithExternalLinks
    const token = partnerSpace.token
      ? partnerSpace.token
      : await generateSpaceToken(partnerSpace._id.toString())

    try {
      partnerSpaces.push({
        id: partnerSpace._id.toString(),
        unitName: partnerSpace?.unit,
        virtualTourUrl: `${WEB_VIEWER_BASE_URL}?token=${token}&pageType=unit`,
        address: partnerSpace.building.address,
        type: partnerSpace.type,
        completed: partnerSpace.isComplete ?? false,
        visible: partnerSpace.isVisible ?? false,
        externalIds: partnerSpace.externalLinks.map((link) => ({
          id: link.externalId,
          service: link.service.toString(),
          name: link.externalName
        })),
        ...(partnerSpace.type === SpaceType.Unit && {
          unitDetails: {
            layout: `${partnerSpace.bedrooms}br/${partnerSpace.bathrooms}ba`,
            layoutType: partnerSpace.floorPlan?.name || null,
            isModelUnit: partnerSpace.isModelUnit
          }
        }),
        building: {
          _id: partnerSpace.building?._id,
          externalIds: partnerSpace.building['externalLinks']?.map((link) => ({
            id: link.externalId,
            service: link.service.toString(),
            name: link.externalName
          }))
        }
      })
    } catch (e) {
      logDebug('Error in getSpacesByCommunityId', e, partnerSpace)
    }
  }

  return partnerSpaces
}

export const getSpaceLink = async (
  spaceId: string,
  organizationId: string,
  service: string,
  email: string,
  phone: string,
  prospectOrigin: ProspectOrigins
) => {
  const space = await getSpace(
    spaceId,
    organizationId,
    service as ExternalLinkService
  )

  if (!space.token) {
    space.token = await generateSpaceToken(space._id.toString())
  }

  let prospect: Prospect
  let pid: string
  if (email || phone) {
    prospect = await prospectsRepo.addOrUpdateProspectByOrgAndContactInfo({
      email: email,
      phone: phone,
      origin: prospectOrigin,
      organizationId,
      communityId: space.community._id.toString()
    })
    pid = prospect?._id && `&pid=${prospect._id}`
  }

  return {
    id: space._id.toString(),
    prospectId: prospect?._id.toString() || null,
    virtualTourUrl: `${getSpaceLinkUrl(space.token, pid)}&pageType=unit`,
    isTracked: prospect ? true : false,
    communityId: space.community._id.toString()
  }
}

export const getSpaceLinkUrl = (token: string, pid?: string) => {
  return `${WEB_VIEWER_BASE_URL}?token=${token}${pid ? `&pid=${pid}` : ''}`
}

export const createLinkContext = async (
  organizationId: string,
  communityId: string,
  spaceId: string,
  prospectId: string,
  email: string,
  phone: string,
  token: string
) => {
  const contextId = `${token}:${nanoid(10)}`
  await contextRepo.createContext<Partial<VirtualTourLinkRequestContext>>(
    contextId,
    removeUndef<VirtualTourLinkRequestContext>({
      organizationId,
      communityId,
      spaceId,
      prospectId,
      email,
      phone,
      token
    })
  )
  return contextId
}

export const getSpace = async (
  id: string,
  organizationId: string,
  service?: ExternalLinkService
) => {
  let space: Space
  if (service)
    space = await spacesRepo.findSpaceByExternalId({
      externalId: id,
      service,
      organizationId,
      fields: 'token community _id'
    })
  else {
    space = await spacesRepo.findSpaceById(id, '_id token community')
  }
  if (!space) throw new NotFoundError('Space not found.')
  if (space.community.organization._id.toString() !== organizationId)
    throw new NotFoundError('Space not found.')

  return space
}
