import Joi from 'joi'
import { SpaceType } from '../types/spaceType'
import { ExternalSpaceStatus, Space } from '../types/space'
import { NodeRequestSchema, NodeSchema } from './node'
import { AddressSchema, BuildingSchema } from './building'

export const GenerateSpaceTokenRequest = Joi.object({
  spaceId: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
})

export const GetSpaceByTokenRequest = Joi.object({
  token: Joi.string().required()
})

export const SpaceSchema: Joi.ObjectSchema<Space> = Joi.object({
  _id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  type: Joi.string().valid(...Object.values(SpaceType)),
  nodes: Joi.array().items(NodeSchema),
  community: Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
    name: Joi.string().example('My Community'),
    organization: Joi.object({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
      name: Joi.string().example('My Organization')
    })
  }),
  building: Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .optional()
      .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
    address: AddressSchema.optional(),
    alternativeName: Joi.string().example('123 Main St').allow('')
  }).optional(),
  startNode: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  unit: Joi.string().example('#1234'),
  bedrooms: Joi.number().example(1),
  bathrooms: Joi.number().example(1),
  salePrice: Joi.number().example(1000000),
  rentPrices: Joi.array().items(
    Joi.object({
      termInMonths: Joi.number().example(12),
      price: Joi.number().example(1000),
      enabled: Joi.boolean().example(true)
    })
  ),
  currency: Joi.string().example('USD'),
  isMultiRes: Joi.boolean().example(true),
  isHorizonLevel: Joi.boolean().example(true),
  isForSale: Joi.boolean().example(false),
  isVisible: Joi.boolean().example(true),
  deletedAt: Joi.date().example('2021-05-05T11:05:33.699Z'),
  amenities: Joi.array().items(Joi.string()).example(['dishwasher', 'dryer']),
  isComplete: Joi.boolean().example(true),
  createdBy: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  grossRent: Joi.number().example(1000),
  description: Joi.string().example('This is a description'),
  floorPlan: Joi.object({
    name: Joi.string().example('Floor Plan'),
    url: Joi.string().uri().example('https://example.com/floor-plan.png'),
    roomPlanFile: Joi.string()
      .example('https://example.com/room-plan.png')
      .optional(),
    roomPlanDimension: Joi.object({
      width: Joi.number().example(100),
      height: Joi.number().example(100),
      length: Joi.number().example(100)
    }).optional(),
    urlChangedAt: Joi.date().example('2020-10-20T12:00:00.000Z')
  }),
  coverPhoto: Joi.object({
    name: Joi.string().example('Cover Photo'),
    url: Joi.string().uri().example('https://example.com/cover-photo.png'),
    urlChangedAt: Joi.date().example('2020-10-20T12:00:00.000Z')
  }),
  showContactForm: Joi.boolean().example(true),
  canBypassContactForm: Joi.boolean().example(true),
  isModelUnit: Joi.boolean().example(true),
  publishGmbStatus: Joi.string().example('PUBLISHED'),
  gmbUser: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  unitSize: Joi.number().example(1000),
  externalSpaceStatus: Joi.string().valid(
    ...Object.values(ExternalSpaceStatus)
  ),
  sgtEnabled: Joi.boolean().example(true),
  availableDate: Joi.date().example('2020-10-20T12:00:00.000Z'),
  availabilityStatus: Joi.string().example('AVAILABLE'),
  token: Joi.string().example('12345678'),
  createdAt: Joi.date().example('2020-10-20T12:00:00.000Z'),
  updatedAt: Joi.date().example('2020-10-20T12:00:00.000Z')
})

export const CreateSpaceBodyRequestSchema = Joi.object({
  type: Joi.string()
    .valid(...Object.values(SpaceType))
    .required(),
  nodes: Joi.array().items(
    NodeRequestSchema.keys({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
    })
  ),
  building: BuildingSchema.required()
    .keys({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .optional()
        .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
      address: AddressSchema.optional().keys({
        street: Joi.string().optional(),
        placeId: Joi.string().optional(),
        city: Joi.string().optional(),
        state: Joi.string().optional(),
        country: Joi.string().optional(),
        postalCode: Joi.string().optional(),
        latitude: Joi.number().optional(),
        longitude: Joi.number().optional()
      })
    })
    .optional(),
  community: Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .example('5f9b9b9b9b9b9b9b9b9b9b9b')
      .required(),
    name: Joi.string().optional(),
    organization: Joi.object({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
        .required(),
      name: Joi.string().required()
    }).optional()
  }).required(),
  unit: Joi.string().example('#1234').required(),
  bedrooms: Joi.number().example(1).optional().default(0), // TODO - make required when type is unit
  bathrooms: Joi.number().example(1).optional().default(0), // TODO - make required when type is unit
  salePrice: Joi.number().example(1000000),
  rentPrices: Joi.array()
    .items(
      Joi.object({
        termInMonths: Joi.number().example(12).required(),
        price: Joi.number().example(1000).required(),
        enabled: Joi.boolean().example(true)
      })
    )
    .optional(),
  currency: Joi.string().example('USD'),
  thumbnails: Joi.array().items(
    Joi.string().uri().example('https://example.com')
  ),
  isMultiRes: Joi.boolean().example(true),
  isHorizonLevel: Joi.boolean().example(true),
  isForSale: Joi.boolean().example(false),
  isVisible: Joi.boolean().example(true).required(),
  amenities: Joi.array().items(Joi.string()).example(['dishwasher', 'dryer']),
  isComplete: Joi.boolean().example(true).required(),
  description: Joi.string().example('This is a description'),
  floorPlan: Joi.object({
    name: Joi.string().example('Floor Plan'),
    url: Joi.string().example('https://example.com/floor-plan.png'),
    roomPlanFile: Joi.string()
      .example('https://example.com/room-plan.png')
      .optional(),
    roomPlanDimension: Joi.object({
      width: Joi.number().example(100),
      height: Joi.number().example(100),
      length: Joi.number().example(100)
    }).optional()
  }),
  coverPhoto: Joi.object({
    name: Joi.string().example('Cover Photo').required(),
    url: Joi.string().example('https://example.com/cover-photo.png').required()
  }),
  showContactForm: Joi.boolean().example(true),
  canBypassContactForm: Joi.boolean().example(true),
  isModelUnit: Joi.boolean().example(true),
  unitSize: Joi.number().example(1000),
  externalSpaceStatus: Joi.string().valid(
    ...Object.values(ExternalSpaceStatus)
  ),
  sgtEnabled: Joi.boolean().example(true),
  availableDate: Joi.date().example('2020-10-20T12:00:00.000Z'),
  availabilityStatus: Joi.string().example('AVAILABLE')
})

export const CreateSpaceResponseSchema = Joi.object({
  data: SpaceSchema
})

// Get space by id
export const GetSpaceResponseSchema = Joi.object({
  data: SpaceSchema
})

// Update space
export const UpdateSpaceBodyRequestSchema = Joi.object({
  type: Joi.string().valid(...Object.values(SpaceType)),
  nodes: Joi.array().items(
    NodeRequestSchema.keys({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
    })
  ),
  building: Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .optional()
      .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
    address: AddressSchema.optional().keys({
      street: Joi.string().optional(),
      placeId: Joi.string().optional(),
      city: Joi.string().optional(),
      state: Joi.string().optional(),
      country: Joi.string().optional(),
      postalCode: Joi.string().optional(),
      street2: Joi.string().optional().allow(null, '')
    })
  }),
  unit: Joi.string().example('#1234'),
  bedrooms: Joi.number().example(1).optional().allow(''), // TODO - make required when type is unit
  bathrooms: Joi.number().example(1).optional().allow(''), // TODO - make required when type is unit
  salePrice: Joi.number().example(1000000),
  rentPrices: Joi.array()
    .items(
      Joi.object({
        termInMonths: Joi.number().example(12).required(),
        price: Joi.number().example(1000).required().allow(''),
        enabled: Joi.boolean().example(true),
        _id: Joi.string().optional().allow('')
      }).optional()
    )
    .optional(),
  grossRent: Joi.number().example(1250).allow(''),
  layoutType: Joi.string().example('A2').allow(''),
  currency: Joi.string().example('USD'),
  thumbnails: Joi.array().items(
    Joi.string().uri().example('https://example.com')
  ),
  isMultiRes: Joi.boolean().example(true),
  isHorizonLevel: Joi.boolean().example(true),
  isForSale: Joi.boolean().example(false),
  isVisible: Joi.boolean().example(true),
  amenities: Joi.array()
    .items(Joi.string().optional().allow(''))
    .example(['dishwasher', 'dryer']),
  isComplete: Joi.boolean().example(true),
  description: Joi.string()
    .allow(null)
    .allow('')
    .optional()
    .example('This is a description'),
  floorPlan: Joi.object({
    name: Joi.string().example('Floor Plan').allow(''),
    url: Joi.string().example('https://example.com/floor-plan.png').allow(''),
    roomPlanFile: Joi.string()
      .example('https://example.com/room-plan.png')
      .optional(),
    roomPlanDimension: Joi.object({
      width: Joi.number().example(100),
      height: Joi.number().example(100),
      length: Joi.number().example(100)
    }).optional()
  }).optional(),
  coverPhoto: Joi.object({
    name: Joi.string().example('Cover Photo').required().allow(''),
    url: Joi.string()
      .example('https://example.com/cover-photo.png')
      .required()
      .allow(''),
    urlChangedAt: Joi.string().example(
      'Tue Feb 07 2023 12:43:44 GMT-0300 (Brasilia Standard Time)'
    )
  }).optional(),
  showContactForm: Joi.boolean().example(true),
  canBypassContactForm: Joi.boolean().example(true),
  isModelUnit: Joi.boolean().example(true),
  unitSize: Joi.number().example(1000).allow(''),
  externalSpaceStatus: Joi.string().valid(
    ...Object.values(ExternalSpaceStatus)
  ),
  sgtEnabled: Joi.boolean().example(true),
  availableDate: Joi.date().example('2020-10-20T12:00:00.000Z'),
  availabilityStatus: Joi.string().example('AVAILABLE'),
  startNode: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  accessDevices: {
    devices: Joi.array().items(
      Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
    ),
    tourInstructions: Joi.string().example('This is a tour instruction')
  },
  scheduledFor: Joi.date().example('2020-10-20T12:00:00.000Z').optional(),
  display: Joi.object({
    settings: Joi.object({
      syncDisabledFields: Joi.array().items(Joi.string())
    }),
    values: Joi.object()
  })
})

export const UpdateSpaceQueryRequestSchema = Joi.object({
  processImage: Joi.boolean().example(false)
})

export const UpdateSpaceResponseSchema = Joi.object({
  data: SpaceSchema
})

export const Generate2DImageBody = Joi.object({
  imageUrl: Joi.string()
    .required()
    .uri()
    .example('https://example.com/image.png'),
  fieldOfView: Joi.number().required().example(90),
  pitch: Joi.number().required().example(0),
  yaw: Joi.number().required().example(0)
})

export const BulkUpdateSpacesBodyRequestSchema = Joi.array().items(
  Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
    community: Joi.object({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
    })
  })
)

export const MoveSpacesEventSchema = Joi.object({
  items: Joi.array()
    .items(
      Joi.object({
        spaceId: Joi.string()
          .regex(/^[0-9a-fA-F]{24}$/)
          .required()
          .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
        communityId: Joi.string()
          .regex(/^[0-9a-fA-F]{24}$/)
          .required()
          .example('5f9b9b9b9b9b9b9b9b9b9b'),
        desiredCommunityId: Joi.string()
          .regex(/^[0-9a-fA-F]{24}$/)
          .required()
          .example('5f9b9b9b9b9b9b9b9b9b9b'),
        desiredBuildingId: Joi.string()
          .regex(/^[0-9a-fA-F]{24}$/)
          .required()
          .example('5f9b9b9b9b9b9b9b9b9b9b')
      }).required()
    )
    .required()
})

export const DeduplicateBodyRequestSchema = Joi.object({
  scannerId: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  syncedId: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
    .example('5f9b9b9b9b9b9b9b9b9b9b')
})
