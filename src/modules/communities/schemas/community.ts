import Joi from 'joi'
import {
  Community,
  CommunityPlan,
  CommunityTours,
  planFeatures
} from '../types/community'
import { z } from 'zod'
import { AddressSchema } from '@modules/communities/schemas/building'

export const GeoPlaceSchema = Joi.object({
  name: Joi.string().example('BART'),
  latitude: Joi.number().example(37.7749),
  longitude: Joi.number().example(-122.4194),
  walkingDuration: Joi.number().example(10),
  drivingDuration: Joi.number().example(5),
  transitLine: Joi.array().items(Joi.string()).example(['BART']),
  mode: Joi.string().example('transit')
})

export const CommunitySchema: Joi.ObjectSchema<Community> = Joi.object({
  name: Joi.string().example('My Community'),
  organization: Joi.object({
    _id: Joi.string()
      .regex(/^[0-9a-fA-F]{24}$/)
      .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
    name: Joi.string().example('My Organization')
  }),
  amenities: Joi.array().items(Joi.string()).example(['pool', 'gym']),
  amenitiesDisplayOrder: Joi.array()
    .items(Joi.string())
    .example(['630f725c43ec62001a2e091c', '252325c43ec62001a2e09asdf']),
  communityStyle: Joi.object({
    primaryColor: Joi.string().example('#000000').allow(''),
    secondaryColor: Joi.string().example('#ffffff').allow(''),
    backgroundColor: Joi.string().example('#ffffff').allow(''),
    ctaColor: Joi.string().example('#000000').allow('')
  }),
  communityInfo: Joi.object({
    logo: Joi.string().example('https://example.com/logo.png').allow(''),
    url: Joi.string().uri().example('https://example.com').allow(''),
    applyUrl: Joi.string().uri().example('https://example.com/apply').allow(''),
    applyText: Joi.string().example('Apply Now').allow(''),
    customJs: Joi.string().example('console.log("Hello World")').allow(''),
    gaTrackingId: Joi.string().example('UA-66894520-00').allow(''),
    displayBuildingName: Joi.boolean().example(true).optional()
  }),
  communityContact: Joi.object({
    contactName: Joi.string().example('John Doe').allow(''),
    contactPhone: Joi.string().example('************').allow(''),
    contactInstructions: Joi.string().example('Please call me').allow('')
  }),
  plan: Joi.string()
    .valid(...Object.values(CommunityPlan))
    .example('Marketing Suite'),
  description: Joi.string().example('This is a description').allow(''),
  canBypassContactForm: Joi.boolean().example(false),
  defaultScannedVisibility: Joi.boolean().example(false),
  autoCancelUnavailableWalkabouts: Joi.boolean().example(false),
  isActive: Joi.boolean().example(true),
  leadsEmail: Joi.string().email().example('<EMAIL>'),
  nodes: Joi.array().items(Joi.string()).example(['5f9b9b9b9b9b9b9b9b9b9b9b']),
  showContactForm: Joi.boolean().example(true),
  autoDisplayContactFormNavigationCount: Joi.number().example(0).optional(),
  stagedNodes: Joi.array()
    .items(Joi.string())
    .example(['5f9b9b9b9b9b9b9b9b9b9b9b']),
  startNode: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),

  startStagedNode: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b'),
  timezone: Joi.string().example('America/New_York'),
  mapLink: Joi.string().example('www.communityA.com/map').allow(''),
  guideLink: Joi.string().example('www.communityA.com/guide').allow(''),
  sgtFollowUpDelayInMinutes: Joi.number().example(60),
  features: Joi.array()
    .items(Joi.string().valid(...Object.values(planFeatures)))
    .example(['SGT', 'Analytics']),
  displayPriceField: Joi.string(),
  metroArea: Joi.string().example('New York').optional(),
  hasPerimeterAccess: Joi.boolean().example(false).optional(),
  sgtSupportPhoneNumber: Joi.string().allow(''),
  showPrices: Joi.boolean().example(true).optional(),
  autoScanConfig: Joi.object({
    offsetDays: Joi.number().example(1),
    dateField: Joi.string().example('date'),
    statusFields: Joi.array().items(
      Joi.object({
        field: Joi.string().example('status'),
        values: Joi.array().items(Joi.any()).example(['Available', 'Pending'])
      })
    ),
    daysToExpireCanceledRequests: Joi.number().example(1)
  }).optional(),
  pointOfInterest: Joi.object({
    nearbyTransit: Joi.array().items(GeoPlaceSchema),
    nearbyGrocery: GeoPlaceSchema,
    nearbyGym: GeoPlaceSchema,
    nearbyPark: GeoPlaceSchema
  }).optional(),
  address: AddressSchema.optional(),
  showAddress: Joi.boolean().example(true).optional(),
  useDxSGTScheduler: Joi.boolean().example(true).optional(),
  regionalLeasingSettings: Joi.object({
    enabled: Joi.boolean().example(true),
    triggerTime: Joi.number().example(10),
    priceRange: Joi.number().example(2),
    communitiesGroup: Joi.array().items(Joi.string()).example(['id1', 'id2'])
  }).optional(),
  tours: Joi.object().pattern(
    Joi.string().valid(CommunityTours.AGT, CommunityTours.SGT),
    Joi.object({
      isAvailable: Joi.boolean().default(false),
      label: Joi.string().allow(null).allow('').optional()
    })
  )
})

export const CommunityWithIdSchema = CommunitySchema.append({
  _id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .example('5f9b9b9b9b9b9b9b9b9b9b9b')
})

export const CommunityWithBuildingsSchema = CommunitySchema.append({
  buildings: Joi.array().items(
    Joi.object({
      _id: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .example('5f9b9b9b9b9b9b9b9b9b9b9b')
    })
  )
})

export const CommunitiesQuery = Joi.object({
  name: Joi.string().optional().allow(''),
  organizationId: Joi.string().optional(),
  offset: Joi.number().default(0),
  limit: Joi.number().default(10),
  orderBy: Joi.string().default('createdAt').allow('name', 'createdAt'),
  order: Joi.string().default('desc').allow('asc', 'desc')
})

// Get Community By Id
export const GetCommunityByIdResponseSchema = Joi.object({
  data: CommunityWithIdSchema
})

// Get Communities
export const GetCommunitiesRequestParamsSchema = CommunityWithIdSchema.append({
  offset: Joi.number().default(0),
  limit: Joi.number().default(10),
  orderBy: Joi.string().default('createdAt').allow('name', 'createdAt'),
  order: Joi.string().default('desc').allow('asc', 'desc')
})

export const GetCommunitiesResponseSchema = Joi.object({
  data: Joi.array().items(GetCommunityByIdResponseSchema),
  totalCount: Joi.number().example(1)
})

export const SlotTimesDateParamRequest = Joi.object({
  date: Joi.date().required(),
  tourType: Joi.string()
})

// Update Community
export const UpdateCommunityBodyRequest = CommunitySchema
export const UpdateCommunityResponseSchema = Joi.object({
  data: CommunityWithIdSchema
})

// Create Community
export const CreateCommunityBodyRequest = CommunityWithBuildingsSchema
export const CreateCommunityResponseSchema = Joi.object({
  data: CommunityWithIdSchema
})

export const CompatibleListingUnitsRequest = Joi.object({
  agent_ids: Joi.array()
    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
    .optional(),
  building_ids: Joi.array()
    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
    .optional(),
  is_model_unit: Joi.boolean().optional(),
  layout_types: Joi.array().optional(),
  units: Joi.array().optional()
}).unknown(true)

export const CompatibleSpaceTokenRequest = Joi.object({
  listing_id: Joi.string().required()
}).unknown(true)

export const CompatibleSpaceLink = Joi.object({
  agent_id: Joi.string().required()
}).unknown(true)

export const CommunityPriceFieldsParamRequest = Joi.object({
  id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
})

export const UpdateCASACommunityBodySchema = z.object({
  name: z.string(),
  assistantName: z.string(),
  handoffEmail: z.string().email(),
  contextUrls: z.array(z.string().url()).optional(),
  widgetConfig: z
    .object({
      autoDisplayTime: z.number().optional(),
      primaryColor: z.string().optional(),
      greetingMessage: z.string().optional(),
      commonQuestions: z.array(z.string()).optional()
    })
    .optional()
})

const JoiObjectId = Joi.string().regex(/^[0-9a-fA-F]{24}$/)
export const CommunityIdSchema = Joi.object({
  id: JoiObjectId.required()
})
