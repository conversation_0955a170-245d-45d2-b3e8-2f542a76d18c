import { Organization } from '@modules/users/types/organization'
import { CommunityTemplate } from '../routes/templates/community'
import { Building, PopulateBuilding } from './building'
import { Id } from './id'
import { Space } from './space'
import { PopulateDXSetting } from './dxSetting'
import { Address } from './address'

export enum CommunityTours {
  AGT = 'AGT',
  SGT = 'SGT'
}

export enum CommunityPlan {
  MarketingSuite = 'Marketing Suite',
  TotalLeasing = 'Total Leasing',
  Leasing3D = '3D Leasing',
  Inactive = 'Inactive'
}

export interface GeoPlace {
  name: string
  latitude: number
  longitude: number
  walkingDuration?: number
  drivingDuration?: number
  transitLine?: string[]
  mode?: string
}

export type Assumptions = {
  estimateTourSaveRate: number
  estimateLaborRate: number
  schedulingOverhead: number
  avgTimeSpentOnsite: number
  avgQACall: number
  questionRate: number
}

export type PopulateCommunity = Community & {
  buildings: PopulateBuilding[]
  dxSettings?: PopulateDXSetting
  spaces?: Space[]
  toObject?: () => PopulateCommunity
}

export type CommunityPointOfInterest = {
  nearbyTransit?: GeoPlace[]
  nearbyGrocery?: GeoPlace
  nearbyGym?: GeoPlace
  nearbyPark?: GeoPlace
}

export type RegionalLeasingSettings = {
  enabled: boolean
  priceRange: number
  triggerTime: number
  communitiesGroup: string[]
}

export interface Community {
  _id: Id | string
  name: string
  startNode?: Id
  organization: Pick<Organization, '_id' | 'name'>
  leadsEmail: string
  isActive: boolean
  description: string
  communityInfo?: CommunityInfo
  amenities?: string[]
  amenitiesDisplayOrder?: string[]
  showContactForm: boolean
  canBypassContactForm: boolean
  autoDisplayContactFormNavigationCount?: number
  defaultScannedVisibility?: boolean
  autoCancelUnavailableWalkabouts?: boolean
  communityContact?: CommunityContact
  timezone?: string
  communityStyle?: CommunityStyle
  plan: CommunityPlan
  buildings?: Building[] | string[]
  createdAt?: Date
  updatedAt?: Date
  mapLink?: string
  guideLink?: string
  assumptions: Assumptions
  sgtFollowUpDelayInMinutes?: number
  features?: string[]
  hasPerimeterAccess?: boolean
  displayPriceField: string
  metroArea?: string
  metroAreaInfo?: {
    administrative_area_level_1?: string
    administrative_area_level_2?: string
    locality?: string
    sublocality_level_1?: string
  }
  sgtSupportPhoneNumber?: string
  showPrices?: boolean
  autoScanConfig?: {
    offsetDays: number
    dateField: string
    statusFields: StatusFieldType[]
    daysToExpireCanceledRequests: number
  }
  pointOfInterest: CommunityPointOfInterest
  address: Address
  showAddress?: boolean
  useDxSGTScheduler?: boolean
  updatedBy?: string
  regionalLeasingSettings?: RegionalLeasingSettings
  tours: {
    [CommunityTours.AGT]: { isAvailable: boolean; label?: string }
    [CommunityTours.SGT]: { isAvailable: boolean; label?: string }
  }
}
export type StatusFieldType = {
  field: string
  values: string[]
}

export interface CommunityContact {
  contactName?: string
  contactPhone?: string
  contactInstructions?: string
}

export interface CommunityInfo {
  logo?: string
  url?: string
  applyUrl?: string
  applyText?: string
  customJs?: string
  gaTrackingId?: string
  displayBuildingName?: boolean
}

export interface CommunityStyle {
  primaryColor?: string
  secondaryColor?: string
  backgroundColor?: string
  ctaColor?: string
}

export type CommunityQuery = Partial<Community> & {
  offset?: number
  limit?: number
  orderBy?: 'createdAt' | 'name'
  order?: 'asc' | 'desc'
  ids?: string[]
  organizationIds?: string[]
  template?: CommunityTemplate
}

export type CommunityOptions = {
  _id?: number
  name?: number
  organizationId?: number
  isActive?: number
}

export type DefaultOptions = {
  offset?: number
  limit?: number
}

export type NodesByIdListQuery = DefaultOptions & {
  nodes: Id[] | string[]
}

export type SpaceWithBuildings = Space & {
  buildings: Building[]
}

export type BuildingDto = {
  _id?: string
  address: string
  zipCode: string
}

export type CommunityDTO = {
  plan?: CommunityPlan
  name?: string
  buildings?: BuildingDto[]
  phone?: string
  organizationId?: string
  leadsEmail?: string
}

export type CompatibleCommunitySpaces = {
  status: string
  message: string
  data?: {
    result: CompatibleCommunitySpacesResult[]
  }
}

export type CompatibleCommunitySpacesResult = {
  Listing_id: string
  address: string
  Unit: string
  'Postal Code': string
  'Vimeo URL': string
  Bedrooms: number
  Bathrooms: number
  Status: string
  'Layout Type': string
  'Is Model Unit': boolean
  'Virtual Tour URL': string
  'Date Uploaded': string
}

export type GetCommunitiesByOrgParams = {
  organizationId: string
  deletedAt?: Date
  isActive?: boolean
  fields?: string
}

export const plansFeature = {
  'Total Leasing': ['Prospect Intelligence', 'Analytics', 'SGT'],
  '3D Leasing': ['Prospect Intelligence', 'Analytics'],
  'Marketing Suite': []
}

export enum planFeatures {
  ProspectIntelligence = 'Prospect Intelligence',
  SGT = 'SGT',
  Analytics = 'Analytics',
  SGT_CASA = 'SGT_CASA',
  VT_CASA = 'VT_CASA',
  Widget_CASA = 'Widget_CASA',
  MagicButton = 'Magic Button',
  AutoScan = 'AutoScan',
  SelfScanning = 'Self_Scanning'
}
