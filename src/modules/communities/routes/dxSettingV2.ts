import { Request, Response, NextFunction } from 'express'
import { AxiosError } from 'axios'
import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { makeInternalApiV2Request } from '@core/request/peekInternalApiClient'
import { getAclScopeQuery } from '@core/auth'
import { DXSettingServicesV2 } from '@modules/communities/types/dxSetting'

const V2_DX_SETTING = [DXSettingServicesV2.ELISE_AI]
const location = 'DxSettingProxyMiddleware'

export const dxSettingProxyMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { service } = req.params

  const shouldProxyToLegacy = !V2_DX_SETTING.includes(
    service as DXSettingServicesV2
  )

  if (shouldProxyToLegacy) {
    logInfo(location, `Routing to local /dx-settings for service: ${service}`)
    next()
    return
  }

  logInfo(location, `Routing to Internal API Gateway for service: ${service}`)

  try {
    const aclQuery = getAclScopeQuery<{
      communityId?: { $in: Array<string> }
    }>()

    const communitiesToFilter =
      aclQuery.communityId?.$in.map((id) => id.toString()) || []

    if (!communitiesToFilter?.length) {
      req.query.communityId = communitiesToFilter
    }

    const result = await makeInternalApiV2Request(
      `/dx-settings${req.path}`,
      req.method as 'GET' | 'POST' | 'PUT' | 'DELETE',
      req.body,
      undefined,
      req.query as Record<string, string>
    )

    logDebug(location, 'DxSetting response', { response: result })
    res.status(200).json(result)
  } catch (error) {
    console.error('Error in dxSettingProxyMiddleware', error)
    if (error instanceof AxiosError) {
      logWarn(location, 'DxSetting error', {
        error: error.response?.data
      })
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'Internal Server Error',
        error: error.response?.data?.data
      })
    } else {
      logError(location, 'DxSetting error', { error: (error as Error).message })
      res.status(500).json({ message: (error as Error).message })
    }
  }
}
