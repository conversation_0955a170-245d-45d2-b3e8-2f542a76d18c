import { mongoModelAclHook } from '@core/auth'
import { AccessDeviceTreeNodeModel } from '@modules/sgt/models/accessDeviceTreeNode'
import { Model, model, Schema, Types } from 'mongoose'
import { nanoid } from 'nanoid'
import { Node } from '../types/node'
import {
  DisplaySpace,
  ExternalSpaceStatus,
  FloorPlan,
  RentPrice,
  Space,
  SpaceDxUpdate,
  SpaceDxUpdateData
} from '../types/space'
import { SpaceType } from '../types/spaceType'
import { nodeSchema } from './node'
import { AccessDeviceModel } from '@modules/sgt/models/accessDevice'
type SpaceDocumentProps = {
  nodes: Types.DocumentArray<Node>
}

// eslint-disable-next-line
type SpaceModelType = Model<Space, {}, SpaceDocumentProps>

const floorPlanSchema = new Schema<FloorPlan>(
  {
    externalId: { type: String },
    name: { type: String },
    roomPlanFile: {
      type: String,
      get: function (url: string) {
        if (!url) return ''
        if (url.includes('https://') || url.includes('http://')) return url
        return `${process.env.IMAGE_BUCKET_BASE_URL}/${url}`
      }
    },
    url: {
      type: String,
      get: function (url: string) {
        if (!url) return ''
        if (url.includes('https://') || url.includes('http://')) return url
        return `${process.env.IMAGE_BUCKET_BASE_URL}/floor-plans/${url}`
      }
    },
    urlChangedAt: { type: Date, default: null },
    roomPlanDimension: {
      width: { type: Number },
      height: { type: Number },
      length: { type: Number }
    }
  },
  { toJSON: { getters: true }, id: false }
)

const rentPriceSchema = new Schema<RentPrice>(
  {
    termInMonths: { type: Number },
    price: { type: Number },
    enabled: { type: Boolean }
  },
  { id: false }
)

const dxUpdateDataSchema = new Schema<SpaceDxUpdateData>(
  {
    unit: { type: String },
    bedrooms: { type: Number },
    bathrooms: { type: Number },
    rentPrices: [rentPriceSchema],
    floorPlan: floorPlanSchema,
    pricesMetadata: {
      type: Schema.Types.Mixed
    },
    availabilityStatus: { type: String }
  },
  { id: false }
)

const dxUpdateSchema = new Schema<SpaceDxUpdate>(
  {
    started: { type: Date },
    finished: { type: Date },
    errorMessage: { type: String },
    data: { type: dxUpdateDataSchema }
  },
  { id: false }
)

const displaySchema = new Schema<DisplaySpace>({
  settings: { syncDisabledFields: { type: [String] } },
  values: { type: Schema.Types.Mixed }
})

export const spaceSchema = new Schema<Space>(
  {
    type: { type: String, required: true, enum: SpaceType },
    availableDate: { type: Date },
    readyToRentDate: { type: Date },
    display: {
      type: displaySchema,
      required: false
    },
    nodes: {
      type: [nodeSchema]
    },
    startNode: { type: Schema.Types.ObjectId },
    building: {
      _id: { type: Schema.Types.ObjectId, required: false, ref: 'building' },
      name: {
        type: String,
        required: false
      },
      address: {
        placeId: { type: String },
        street: { type: String, required: true },
        street2: { type: String },
        latitude: { type: Number },
        longitude: { type: Number },
        postalCode: { type: String },
        neighborhood: { type: String },
        state: { type: String },
        district: { type: String },
        city: { type: String },
        country: { type: String },
        number: { type: String }
      },
      alternativeName: { type: String },
      location: {
        type: { type: String, enum: ['Point'], required: false },
        coordinates: { type: [Number], required: false }
      }
    },
    community: {
      _id: { type: Schema.Types.ObjectId, required: true, ref: 'communities' },
      organization: {
        _id: {
          type: Schema.Types.ObjectId,
          required: true,
          ref: 'organizations'
        },
        name: { type: String, required: true }
      },
      name: { type: String, required: true },
      logo: {
        type: String,
        get: (logo: string) => {
          if (!logo) return ''
          return `${process.env.IMAGE_BUCKET_BASE_URL}/branding/${logo}`
        }
      },
      primaryColor: { type: String },
      applyUrl: { type: String },
      applyText: { type: String },
      gaTrackingId: { type: String },
      customJs: { type: String },
      secondaryColor: { type: String },
      showContactForm: { type: Boolean },
      showAddress: { type: Boolean, default: true },
      canBypassContactForm: { type: Boolean },
      autoDisplayContactFormNavigationCount: { type: Number, default: 0 },
      displayBuildingName: {
        type: Boolean,
        default: false
      },
      displayPriceField: {
        type: String,
        default: 'rentPrices.12.price'
      },
      isActive: { type: Boolean },
      metroArea: { type: String }
    },
    unit: { type: String },
    bedrooms: { type: Number },
    bathrooms: { type: Number },
    salePrice: { type: Number },
    rentPrices: [rentPriceSchema],
    currency: { type: String, default: 'USD' },
    isMultiRes: { type: Boolean, default: false, required: true },
    isHorizonLevel: { type: Boolean, default: false, required: true },
    isForSale: { type: Boolean, default: false },
    isVisible: { type: Boolean, default: false },
    deletedAt: { type: Date, default: null },
    amenities: [{ type: String }],
    isComplete: { type: Boolean, default: false, required: true },
    createdBy: { type: Schema.Types.ObjectId, ref: 'user' },
    scanRequested: { type: Boolean, default: false },
    availabilityStatus: { type: String },
    grossRent: { type: Number },
    description: { type: String },
    floorPlan: { type: floorPlanSchema },
    coverPhoto: {
      name: { type: String },
      url: {
        type: String,
        get: function (url: string) {
          if (!url) return ''
          if (url.includes('https://') || url.includes('http://')) return url
          return `${process.env.IMAGE_BUCKET_BASE_URL}/spaces/${url}`
        }
      },
      urlChangedAt: { type: Date, default: null }
    },
    showContactForm: { type: Boolean, default: false },
    canBypassContactForm: { type: Boolean, default: false },
    isModelUnit: { type: Boolean, default: false },
    publishGmbStatus: { type: String, default: 'unpublished' },
    gmbUser: { type: Schema.Types.ObjectId, ref: 'user' },
    gmbImageCheckups: [{ type: Date }],
    unitSize: { type: Number },
    externalSpaceStatus: [{ type: String, enum: ExternalSpaceStatus }],
    sgtEnabled: { type: Boolean, default: false, required: true },
    token: {
      type: String,
      index: {
        sparse: true,
        unique: true
      },
      default: () => nanoid(10)
    },
    publishApartmentsDotCom: { type: Boolean, default: false },
    rentCafeV2VirtualTourSent: { type: String },
    tourCapturedDate: { type: Date },
    tourLastUpdatedDate: { type: Date },
    dxUpdate: { type: dxUpdateSchema },
    accessDevice: {
      leafNode: {
        type: Schema.Types.ObjectId,
        ref: AccessDeviceTreeNodeModel.modelName
      },
      instructions: { type: String }
    },
    pricesMetadata: {
      type: Schema.Types.Mixed
    },
    displayPrice: { type: Number },
    isMarketable: { type: Boolean, default: true },
    updatedBy: { type: String },
    availableDatesMetadata: {
      type: Map,
      of: Date
    },
    availableStatusesMetadata: {
      type: Schema.Types.Mixed
    },
    category: { type: String, default: 'Other' },
    spaceCategory: { type: String },
    spaceFunction: { type: String },
    spaceDetail: { type: String }
  },
  { timestamps: true, id: false }
)

spaceSchema.index({ 'building.location.coordinates': '2dsphere' })

spaceSchema.index(
  { 'community._id': 1, 'building._id': 1, unit: 1, deletedAt: 1 },
  { unique: true }
)
spaceSchema.pre('find', mongoModelAclHook)
spaceSchema.pre('findOne', mongoModelAclHook)
spaceSchema.pre('countDocuments', mongoModelAclHook)

spaceSchema.virtual('scanRequests', {
  ref: 'scanRequest',
  localField: '_id',
  foreignField: 'space._id',
  justOne: false,
  options: {
    sort: { scheduledFor: 1 },
    projection: {
      _id: 1,
      availableFrom: 1,
      status: 1,
      scheduledFor: 1,
      user: 1,
      accessDetails: 1,
      specialInstructions: 1,
      cancelationReason: 1,
      deletedAt: 1,
      scannedAt: 1
    }
  }
})

spaceSchema.virtual('externalLink', {
  ref: 'externalLink',
  localField: '_id',
  foreignField: 'objectId',
  justOne: true,
  match: {
    deletedAt: null,
    collectionPath: 'spaces'
  },
  options: {
    projection: {
      _id: 0,
      objectId: 0,
      service: 1,
      externalId: 1,
      externalName: 1,
      updatedAt: 1,
      createdAt: 1
    }
  }
})

spaceSchema.set('toObject', {
  getters: true
})
spaceSchema.set('toJSON', {
  getters: true
})

export const SpaceModel = model<Space, SpaceModelType>('space', spaceSchema)

export function populateIncludeMap() {
  return {
    accessDevice: {
      path: 'accessDevice.leafNode',
      options: {
        populate: {
          path: 'accessDevice',
          model: AccessDeviceModel.modelName,
          options: {
            skipAclScope: true
          }
        }
      }
    },
    scanRequests: {
      path: 'scanRequests'
    }
  }
}
