import { mongoModelAclHook } from '@core/auth'
import { model, Model, Schema, Types } from 'mongoose'
import { Building } from '../types/building'
import {
  Community,
  CommunityPlan,
  CommunityTours,
  planFeatures
} from '../types/community'
import { Space } from '../types/space'

const geoPlaceSchema = {
  name: { type: String },
  description: { type: String },
  latitude: { type: Number },
  longitude: { type: Number },
  walkingDuration: { type: Number },
  drivingDuration: { type: Number },
  transitLine: { type: [String] }
}

type CommunityDocumentProps = {
  spaces: Types.DocumentArray<Space>
  buildings: Types.DocumentArray<Building>
}
// eslint-disable-next-line
type CommunityModelType = Model<Community, {}, CommunityDocumentProps>

const communitySchema = new Schema<Community, CommunityModelType>(
  {
    name: { type: String, required: true },
    startNode: { type: Schema.Types.ObjectId, ref: 'spaces.nodes' },
    organization: {
      _id: { type: Schema.Types.ObjectId, ref: 'organization', required: true },
      name: { type: String, required: true }
    },
    leadsEmail: { type: String },
    communityInfo: {
      logo: {
        type: String,
        get: function (value: string) {
          return value
            ? `${process.env.IMAGE_BUCKET_BASE_URL}/branding/${value}`
            : ''
        }
      },
      url: { type: String },
      applyUrl: { type: String },
      applyText: { type: String },
      customJs: { type: String },
      gaTrackingId: { type: String },
      displayBuildingName: {
        type: Boolean,
        default: false
      }
    },
    isActive: { type: Boolean, default: true, required: true },
    description: { type: String },
    amenities: [{ type: String }],
    amenitiesDisplayOrder: [{ type: String }],
    showContactForm: { type: Boolean, default: false },
    canBypassContactForm: { type: Boolean, default: true },
    autoDisplayContactFormNavigationCount: { type: Number, default: 0 },
    defaultScannedVisibility: { type: Boolean, default: false },
    autoCancelUnavailableWalkabouts: { type: Boolean, default: false },
    communityContact: {
      contactName: { type: String },
      contactPhone: { type: String },
      contactInstructions: { type: String }
    },
    communityStyle: {
      primaryColor: { type: String },
      secondaryColor: { type: String },
      backgroundColor: { type: String },
      ctaColor: { type: String }
    },
    timezone: { type: String },
    plan: {
      type: String,
      enum: Object.values(CommunityPlan)
    },
    mapLink: {
      type: String,
      required: false
    },
    guideLink: {
      type: String,
      required: false
    },
    assumptions: {
      estimateTourSaveRate: {
        type: Number,
        default: 0.5
      },
      estimateLaborRate: {
        type: Number,
        default: 28
      },
      schedulingOverhead: {
        type: Number,
        default: 0.25
      },
      avgTimeSpentOnsite: {
        type: Number,
        default: 1
      },
      avgQACall: {
        type: Number,
        default: 0.083
      },
      questionRate: {
        type: Number,
        default: 0.3
      }
    },
    sgtFollowUpDelayInMinutes: {
      type: Number,
      required: false,
      default: 60
    },
    features: [
      {
        type: String,
        enum: Object.values(planFeatures)
      }
    ],
    displayPriceField: {
      type: String,
      default: 'rentPrices.12.price'
    },
    metroArea: {
      type: String,
      required: false
    },
    metroAreaInfo: {
      administrative_area_level_1: { type: String },
      administrative_area_level_2: { type: String },
      locality: { type: String },
      sublocality_level_1: { type: String },
      postal_code_suffix: { type: String }
    },
    hasPerimeterAccess: {
      type: Boolean
    },
    sgtSupportPhoneNumber: {
      type: String
    },
    showPrices: {
      type: Boolean,
      default: true
    },
    autoScanConfig: {
      offsetDays: {
        type: Number,
        default: 0
      },
      dateField: {
        type: String
      },
      statusFields: [
        {
          field: {
            type: String
          },
          values: {
            type: [String]
          }
        }
      ],
      daysToExpireCanceledRequests: {
        type: Number,
        default: 7
      }
    },
    pointOfInterest: {
      nearbyTransit: [geoPlaceSchema],
      nearbyGrocery: geoPlaceSchema,
      nearbyGym: geoPlaceSchema,
      nearbyPark: geoPlaceSchema
    },
    address: {
      street: { type: String },
      street2: { type: String },
      latitude: { type: Number },
      longitude: { type: Number },
      postalCode: { type: String },
      neighborhood: { type: String },
      state: { type: String },
      district: { type: String },
      city: { type: String },
      country: { type: String },
      number: { type: String },
      addressString: { type: String },
      placeId: { type: String }
    },
    showAddress: {
      type: Boolean,
      default: true
    },
    // use real-time DX schedule
    useDxSGTScheduler: {
      type: Boolean,
      default: false
    },
    updatedBy: { type: String, default: null },
    regionalLeasingSettings: {
      enabled: {
        type: Boolean,
        default: false
      },
      triggerTime: {
        type: Number,
        default: 20
      },
      priceRange: {
        type: Number,
        default: 2
      },
      communitiesGroup: {
        type: [String],
        default: []
      }
    },
    tours: {
      [CommunityTours.AGT]: {
        isAvailable: { type: Boolean, default: true },
        label: { type: String }
      },
      [CommunityTours.SGT]: {
        isAvailable: { type: Boolean, default: false },
        label: { type: String }
      }
    }
  },
  {
    timestamps: true,
    id: false
  }
)

communitySchema.virtual('buildings', {
  ref: 'building',
  localField: '_id',
  foreignField: 'communityId',
  options: {
    lean: true,
    projection: {
      _id: 1,
      address: 1,
      amenities: 1
    },
    skipAclScope: true
  }
})

communitySchema.virtual('dxSettings', {
  ref: 'dxSetting',
  localField: '_id',
  foreignField: 'communityId',
  justOne: false,
  options: {
    lean: true,
    projection: {
      _id: 1,
      communityId: 0,
      service: 1,
      dataExchange: 1
    }
  }
})

communitySchema.set('toObject', { getters: true })
communitySchema.set('toJSON', { getters: true })

communitySchema.pre('find', mongoModelAclHook)
communitySchema.pre('findOne', mongoModelAclHook)
communitySchema.pre('countDocuments', mongoModelAclHook)

export const CommunityModel = model<Community, CommunityModelType>(
  'community',
  communitySchema
)
