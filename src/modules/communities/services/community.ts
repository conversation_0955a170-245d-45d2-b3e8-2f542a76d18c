import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logInfo, logWarn } from '@core/log'
import { PaginatedResult } from '@core/types'
import { getEnvVariable } from '@core/util'
import {
  Community,
  CommunityQuery,
  CommunityTours,
  GetCommunitiesByOrgParams,
  PopulateCommunity,
  RegionalLeasingSettings
} from '@modules/communities/types/community'
import { Space, SpaceRemovePrices } from '@modules/communities/types/space'
import { mapperCommunitySpace } from '@modules/spacex/dtos/spaceDto'
import * as actionRepo from '@modules/users/repositories/action'
import * as policyRepo from '@modules/users/repositories/policy'
import * as userRepo from '@modules/users/repositories/user'
import { RoleAliases } from '@modules/users/types/role'
import { AuthUser } from '@modules/users/types/user'
import { FilterQuery, UpdateQuery } from 'mongoose'
import * as orgRepo from '../../users/repositories/organization'
import { CommunityModel } from '../models/community'
import * as repo from '../repositories/community'
import { getCommunityTemplate } from '../routes/templates/community'
import { ObjectId } from '../types/id'
import { addOrUpdateBuildingById } from './building'
import { updateCommunityPointOfInterests } from './pointOfInterest'
import {
  getAutoScanFieldsByCommunity,
  getPriceFieldsByCommunity,
  removeSpacesPrices,
  updateSpaceCommunityAndDisplayPrice,
  updateSpaceCommunityById
} from './space'
import { updateSpaceUrls } from '../repositories/space'
import { handleUserCommunitiesOnOrganizationUpdate } from '@modules/users/services/organization'

const location = 'community-service'

const labelAGT = 'Agent-Guided Tour'
const labelSGT = 'Self-Guided Tour'

export const addCommunity = async (
  data: Partial<Community>
): Promise<Community> => {
  const { buildings, ...payload } = data
  const community: Partial<Community> = payload

  const organization = await orgRepo.findOrganizationById(
    data.organization._id.toString()
  )
  if (!organization) throw new NotFoundError('Organization not found')

  community.organization = {
    _id: organization?._id,
    name: organization?.name
  }

  const newCommunity = await repo.addCommunity(community)

  if (buildings) {
    Promise.all(
      buildings.map((building) => {
        return addOrUpdateBuildingById(building._id, {
          communityId: newCommunity._id
        })
      })
    )
  }
  const [orgAdminUsers, orgCommunities, orgAdminAction] = await Promise.all([
    userRepo.getOrgAdminUsersByOrg({
      organizationId: organization._id.toString()
    }),
    repo.findCommunitiesByQuery({
      'organization._id': organization._id.toString()
    }),
    actionRepo.findOneActionByAlias('full')
  ])

  if (orgAdminUsers) {
    await Promise.all(
      orgAdminUsers.map(async (user) => {
        return Promise.all([
          policyRepo.updateUserResourcePoliciesWithAction(
            user._id.toString(),
            'communities',
            orgCommunities?.map((community) => community._id.toString()),
            orgAdminAction
          ),
          userRepo.updateUserById(user._id.toString(), {
            communities: orgCommunities?.map((community) => ({
              _id: community._id.toString(),
              name: community.name
            }))
          })
        ])
      })
    )
  }

  return newCommunity
}

export const addOrUpdateCommunityById = async (
  communityId: string,
  data: Partial<Community>
) => {
  const { ...payload } = data
  const community: UpdateQuery<typeof CommunityModel> = payload
  let existingCommunity = await repo.findCommunityByIdLean(communityId)

  if (data.communityInfo) {
    for (const key of Object.keys(data.communityInfo)) {
      existingCommunity = {
        ...existingCommunity,
        communityInfo: {
          ...existingCommunity.communityInfo,
          [key]: data.communityInfo[key]
        }
      }
    }

    community.communityInfo = existingCommunity.communityInfo
  }

  if (data.communityStyle) {
    for (const key of Object.keys(data.communityStyle)) {
      existingCommunity = {
        ...existingCommunity,
        communityStyle: {
          ...existingCommunity.communityStyle,
          [key]: data.communityStyle[key]
        }
      }
    }

    community.communityStyle = existingCommunity.communityStyle
  }

  if (data.communityContact) {
    for (const key of Object.keys(data.communityContact)) {
      existingCommunity.communityContact[key] = data.communityContact[key]
    }
    community.communityContact = existingCommunity.communityContact
  }

  if (data.organization) {
    const organization = await orgRepo.findOrganizationById(
      data.organization._id.toString()
    )
    community.organization = {
      _id: organization?._id,
      name: organization?.name
    }
  }

  if (data.regionalLeasingSettings) {
    if (!existingCommunity.regionalLeasingSettings) {
      existingCommunity = {
        ...existingCommunity,
        regionalLeasingSettings: {} as RegionalLeasingSettings
      }
    }

    for (const key of Object.keys(data.regionalLeasingSettings)) {
      existingCommunity.regionalLeasingSettings[key] =
        data.regionalLeasingSettings[key]
    }
  }

  if (data.tours) {
    const cleanedTours = { ...data.tours }

    for (const key of Object.keys(cleanedTours)) {
      const label = cleanedTours[key]?.label
      const defaultLabel = key === 'AGT' ? labelAGT : labelSGT

      if (label === '' || label === defaultLabel) {
        delete cleanedTours[key].label
      }
    }

    community.tours = cleanedTours
  }

  if (data?.features?.length && !data?.features?.includes('SGT')) {
    community.tours[CommunityTours.SGT] = {
      isAvailable: false
    }
  }

  const pointOfInterest = await updateCommunityPointOfInterests(
    existingCommunity,
    community as Community
  )

  if (pointOfInterest) {
    try {
      logDebug(location, 'updated point of interest', { pointOfInterest })
      pointOfInterest.nearbyTransit =
        community?.pointOfInterest?.nearbyTransit ||
        existingCommunity?.pointOfInterest?.nearbyTransit ||
        []

      await repo.updateCommunityById(communityId, {
        pointOfInterest
      })
      community.pointOfInterest = pointOfInterest
    } catch (error) {
      logWarn(location, 'error updating point of interest', {
        errorMessage: error.message
      })
    }
  }

  const updatedCommunity = await repo.addOrUpdateCommunityById(
    communityId,
    community
  )

  if (data.organization) {
    const newOrgPayload = { _id: data.organization._id.toString() }

    await handleUserCommunitiesOnOrganizationUpdate(
      existingCommunity,
      newOrgPayload,
      updatedCommunity
    )
  }

  const spaceCommunity = mapperCommunitySpace(updatedCommunity)

  if (data.displayPriceField) {
    community.displayPriceField = data.displayPriceField
    const updatedSpaces = updateSpaceCommunityAndDisplayPrice(
      communityId,
      spaceCommunity
    )
    logDebug(location, 'updated community and  display price field', {
      updatedSpaces
    })
    return updatedCommunity
  }

  const updateSpaceCommunity = await updateSpaceCommunityById(
    communityId,
    spaceCommunity
  )

  logDebug(location, 'updated space community', { updateSpaceCommunity })

  return updatedCommunity
}

export const addOrUpdateCommunityByName = async (
  name: string,
  data: Partial<Community>
) => {
  return repo.addOrUpdateCommunityByName(name, data)
}

export function findCommunitiesByQuery(
  query: FilterQuery<typeof CommunityModel>,
  skipAclScope = false
): Promise<Array<Community>> {
  return repo.findCommunitiesByQuery(query, { skipAclScope })
}

export const findCommunities = async (
  query: CommunityQuery,
  authUser?: AuthUser,
  include?: string,
  version?: any | undefined
): Promise<PaginatedResult<PopulateCommunity>> => {
  const options = authUser
    ? {}
    : { _id: 1, name: 1, organizationId: 1, isActive: 1 }

  const roleAlias = authUser?.roleAlias
  if (!authUser || roleAlias != RoleAliases.ADMIN) {
    query.isActive = true
  }

  if (query.organizationIds) {
    query['organization._id'] = { $in: query.organizationIds }
    delete query.organizationIds
  }

  let projection = ''
  if (query.template) {
    projection = getCommunityTemplate(query.template).projection
    Reflect.deleteProperty(query, 'template')
  }

  let communities: PopulateCommunity[] = []
  let totalCount = 0
  if (version === '1' && include?.includes('spaces')) {
    logInfo(location, 'findCommunities with spaces, version: 1', {
      query,
      options
    })
    ;[communities, totalCount] = await Promise.all([
      repo.findCommunitiesIncludingSpaces(query, projection),
      repo.countCommunities(query)
    ])
  } else {
    ;[communities, totalCount] = await Promise.all([
      repo.findCommunities(
        query,
        options,
        generatePopulate(include),
        projection
      ),
      repo.countCommunities(query)
    ])
  }

  return { data: communities, totalCount }
}

export const findCommunityById = async (
  id: string,
  projection?: string
): Promise<Community | null> => {
  return repo.findCommunityById(id, projection)
}

export const findCommunityByName = async (
  name: string
): Promise<Community | null> => {
  return repo.findCommunityByName(name)
}

const populateIncludeMap = {
  organization: {
    path: 'organization'
  },
  buildings: {
    path: 'buildings'
  },
  spaces: {
    path: 'buildings',
    populate: {
      path: 'spaces',
      match: { deletedAt: null },
      options: {
        lean: false
      }
    }
  },
  dxSettings: {
    path: 'dxSettings'
  }
}

export const findPopulateCommunityById = async (
  id: string,
  include?: string,
  version?: string | undefined
) => {
  if (version === '1') {
    const result = await repo.findCommunitiesIncludingSpaces(
      { _id: new ObjectId(id) },
      undefined
    )

    if (result.length === 0) {
      return undefined
    }

    logInfo(
      location,
      'community',
      result?.map((r) => r._id?.toString())
    )
    if (result[0].spaces) {
      if (result[0].communityInfo?.logo) {
        result[0].communityInfo.logo = logoUrl(result[0].communityInfo.logo)
      }
      result[0].spaces = getFullSpaces(result[0].spaces)
    }

    return result[0]
  }

  const result = (await repo.findPopulateCommunityById(
    id,
    generatePopulate(include)
  )) as PopulateCommunity

  logInfo(location, 'community', result?._id?.toString())

  if (result?.communityInfo?.logo) {
    result.communityInfo.logo = logoUrl(result.communityInfo.logo)
  }

  if (result?.spaces) {
    result.spaces = getFullSpaces(result.spaces)
  }

  return result
}

export const logoUrl = (logo: string) => {
  if (!logo) return ''
  if (logo.includes('https://') || logo.includes('http://')) return logo

  return `${process.env.IMAGE_BUCKET_BASE_URL}/branding/${logo}`
}

const getFullSpaces = (spaces: Space[]) => {
  return spaces.map(updateSpaceUrls)
}

const generatePopulate = (include: string) => {
  let includeList = include?.split(',') ?? []

  const validKeys = Object.keys(populateIncludeMap)
  if (includeList.includes('spaces')) {
    includeList = includeList.filter(
      (includeItem) => includeItem !== 'buildings'
    )
  }

  return includeList.reduce((acc, populateInclude) => {
    if (validKeys.includes(populateInclude)) {
      acc.push(populateIncludeMap[populateInclude])
    }
    return acc
  }, [])
}

export const getCommunitiesSpacesAnalytics = async (query) => {
  return repo.getCommunitiesSpacesAnalytics(query)
}

export const getCommunitiesByOrg = async (
  params: GetCommunitiesByOrgParams
): Promise<Community[]> => {
  return repo.getCommunitiesByOrg(params)
}

export const getCommunityPriceFields = async (communityId: string) => {
  return getPriceFieldsByCommunity(communityId)
}

export const getSgtUrl = (communityId: string) => {
  const WELCOME_SITE_BASE_URL = getEnvVariable('WELCOME_SITE_BASE_URL')
  return `${WELCOME_SITE_BASE_URL}/${communityId}/self-guided-tour`
}

export const removeCommunitySpacePrices = async (
  community: PopulateCommunity,
  version?: string | undefined
) => {
  if (community.showPrices === false) {
    if (community.toObject) community = community.toObject()

    if (version === '1') {
      community.spaces = await removeSpacesPrices(
        community.spaces as SpaceRemovePrices[],
        community
      )
    } else {
      if (community.buildings) {
        for (const building of community.buildings) {
          if (building.spaces && building.spaces.length > 0) {
            building.spaces = await removeSpacesPrices(
              building.spaces as SpaceRemovePrices[],
              community
            )
          }
        }
      }
    }
  }

  return community
}

export const removeCommunitiesSpacesPrices = async (
  communities: PopulateCommunity[],
  version?: string | undefined
) => {
  const result = []
  for (const community of communities) {
    result.push(await removeCommunitySpacePrices(community, version))
  }
  return result
}

export const getCommunityAutoScanConfig = async (communityId: string) => {
  const community = await repo.findCommunityByIdLean(communityId)
  if (!community) throw new NotFoundError('Community not found')
  const autoScanFields = await getAutoScanFieldsByCommunity(communityId)
  return {
    autoScanFields,
    autoScanConfig: community.autoScanConfig
  }
}
