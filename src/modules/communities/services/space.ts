import { getAuthUser } from '@core/auth'
import BadRequestError from '@core/errors/badRequestError'
import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { KLAVIYO_EVENTS, sendMail } from '@core/mail'
import { publish } from '@core/sqs'
import { PaginatedResult } from '@core/types'
import {
  convertCoordinateToPitchAndYaw,
  getEnvVariable,
  pick
} from '@core/util'
import {
  fetchGmbPlaces,
  getGmbAccessToken
} from '@modules/communities/gateway/gmb'
import { findAllDxSettings } from '@modules/communities/services/dxSetting'
import { findDoorsInWall } from '@modules/communities/services/roomplan/findDoorsInWall'
import { DXSettingServices } from '@modules/communities/types/dxSetting'
import {
  buildSyncSuccess,
  markSyncFail
} from '@modules/dataExchange/services/visibility'
import { SpaceEventBody } from '@modules/dataExchange/types/dataExchange'
import { linkDevicesInOrder } from '@modules/sgt/services/accessDevice'
import { findDisplayPrice } from '@modules/spacex/services/price'
import { addOrUpdateSpaceById as addOrUpdateSpaceX } from '@modules/spacex/services/spaceService'
import { CreateOrUpdateSpace, SendScanEmail } from '@modules/spacex/types/space'
import { findUserPoliciesByCommunity } from '@modules/users/services/policy'
import axios from 'axios'
import mongoose, { PopulateOptions } from 'mongoose'
import { nanoid } from 'nanoid'
import { populateIncludeMap, SpaceModel } from '../models/space'
import * as buildingRepo from '../repositories/building'
import * as gmbLocationRepo from '../repositories/gmbLocation'
import * as repo from '../repositories/space'
import * as nodeRepo from '../repositories/node'
import { getSpaceTemplate, SpaceTemplate } from '../routes/templates/space'
import {
  GmbPlacesQuery,
  PublishSpaceToGMBPayload,
  UnpublishSpaceToGMBPayload
} from '../types/gmbLocation'
import { Marker, Node, NodeLinkDto, NodeWithLinksDTO } from '../types/node'
import { NodeLinkType } from '../types/nodeLink'
import {
  CoupleResponse,
  CoupleSpaceIds,
  priceFields,
  RemovePrice,
  QueryOptions,
  Space,
  SpaceCommunity,
  SpaceQuery,
  SpaceRemovePrices,
  UpdateSpaceByIdQuery,
  GetAutoScanFieldsByCommunity
} from '../types/space'
import { SpaceType } from '../types/spaceType'
import { findCommunityById } from './community'
import { findExternalLinks, updateExternalLinkById } from './externalLink'
import { createNodeLinksByMarkers } from './roomplan/createNodeLinksByMarkers'
import { findMarkersLinks } from './roomplan/findMarkersLinks'
import { Community } from '../types/community'
import { get, unset } from 'lodash'
import { Building } from '../types/building'

const location = 'space-service'

const applyDisplayOverrides = (space: Space): Space => {
  const raw =
    typeof (space as any).toObject === 'function'
      ? (space as any).toObject({ getters: true })
      : space

  if (!raw.display?.settings?.syncDisabledFields || !raw.display?.values) {
    return raw
  }

  const overriddenFields = raw.display.settings.syncDisabledFields
  const overrideValues = raw.display.values

  overriddenFields.forEach((field) => {
    if (overrideValues[field] !== undefined) {
      raw[field] = overrideValues[field]
    }
  })

  return raw
}

/** @deprecated - use spacex addOrUpdateSpaceById instead */
export const addOrUpdateSpaceById = async (
  id: string,
  data: Partial<Space>
) => {
  return repo.addOrUpdateSpaceById(id, data)
}

/** @deprecated use spacex addOrUpdateSpaceById instead */
export const addSpace = async (data: Partial<Space>) => {
  // A community must exist before the space and linking both is required
  const existingCommunity = await findCommunityById(
    data.community?._id?.toString()
  )
  if (!existingCommunity) throw new NotFoundError('Community not found.')

  const community = {
    _id: existingCommunity._id,
    name: existingCommunity.name,
    organization: existingCommunity.organization,
    ...existingCommunity.communityInfo,
    ...existingCommunity.communityStyle,
    showContactForm: existingCommunity.showContactForm,
    canBypassContactForm: existingCommunity.canBypassContactForm,
    autoDisplayContactFormNavigationCount:
      existingCommunity.autoDisplayContactFormNavigationCount,
    displayPriceField: existingCommunity.displayPriceField,
    isActive: existingCommunity.isActive,
    showAddress: existingCommunity.showAddress
  }
  data.community = community
  data.displayPrice = findDisplayPrice({
    displayPriceField: community.displayPriceField,
    rentPrices: data.rentPrices || [],
    pricesMetadata: data.pricesMetadata || {}
  })
  // Now we may be creating a new building or linking to an existing one
  let existingBuilding

  // if sends _id, we are linking to an existing building and that building must exist
  if (data.building._id) {
    existingBuilding = await buildingRepo.findBuildingById(
      data.building?._id?.toString()
    )
    if (!existingBuilding) throw new NotFoundError('Building not found.')
  } else if (data.building.address?.placeId) {
    // if sends placeId we are creating a new building or linking to an existing one
    existingBuilding = await buildingRepo.findBuildingByPlaceId(
      data.building.address.placeId
    )
  }

  // if we found an existing building, we link to it
  if (existingBuilding) {
    // but first we check if the existing building belongs to the same community
    if (
      existingBuilding.communityId.toString() !== data.community._id.toString()
    ) {
      throw new NotFoundError('Building not found.')
    }
    data.building = { ...existingBuilding }
  } else {
    // if we didn't find an existing building, we create a new one
    data.building = await buildingRepo.addBuilding({
      ...data.building,
      communityId: data.community._id
    })
  }

  const existingSpace = await repo.findUniqueSpace(
    data.unit,
    data.type,
    data.building._id.toString(),
    data.community._id.toString()
  )

  if (existingSpace) {
    throw new BadRequestError('Space already exists.')
  }

  if (!data.floorPlan) {
    data.floorPlan = { name: '' }
  }

  return repo.addSpace(data)
}

export const updateSpaceById = async (
  id: string,
  data: CreateOrUpdateSpace,
  options: UpdateSpaceByIdQuery = {}
) => {
  const { processImage } = options

  if (data?.rentPrices?.length) {
    const existingSpace = await repo.findSpaceById(
      id,
      'community.displayPriceField pricesMetadata'
    )
    data.displayPrice = findDisplayPrice({
      displayPriceField: existingSpace.community.displayPriceField,
      rentPrices: data.rentPrices || [],
      pricesMetadata: existingSpace.pricesMetadata || {}
    })
  }

  if (data?.nodes?.length) {
    logDebug(
      'services/space',
      `Nodes have changed for space with id ${id}, calculating rotation...`,
      data.nodes
    )

    data.nodes.forEach((node) => {
      if (node.nodeLinks?.length) {
        node.nodeLinks.forEach((nodeLink) => {
          if (nodeLink.position && !nodeLink.rotation) {
            nodeLink.rotation = convertCoordinateToPitchAndYaw(
              nodeLink.position
            )
          }
        })
      }
    })

    if (processImage) {
      const updatedSpace = await repo.addOrUpdateSpaceById(id, data)
      sendScanEmails({
        communityId: updatedSpace.community._id.toString(),
        street: updatedSpace.building?.address?.street,
        unit: updatedSpace.unit
      })
      return processVirtualTour(id)
    }
  }

  if (data?.accessDevices) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    data.accessDevice = {}
    if (data.accessDevices.devices?.length) {
      const leafDevice = await linkDevicesInOrder(
        data?.accessDevices.devices,
        id
      )
      data.accessDevice.leafNode = leafDevice._id
    }

    if (data.accessDevices.tourInstructions) {
      data.accessDevice.instructions = data.accessDevices.tourInstructions
    }

    delete data.accessDevices
  }

  const space = await repo.addOrUpdateSpaceById(id, data)
  return space ? applyDisplayOverrides(space) : null
}

/** @deprecated use processVirtualTour from spacex virtualTourService instead */
export const processVirtualTour = async (spaceId: string): Promise<Space> => {
  const space = await repo.findOneSpaceByQuery({ _id: spaceId })
  const community = await findCommunityById(space.community._id.toString())

  const isVisible = community.defaultScannedVisibility || space.isVisible
  const tourCapturedDate = space.tourCapturedDate || new Date()
  const tourLastUpdatedDate = new Date()

  return repo.addOrUpdateSpaceByQuery(
    { _id: spaceId },
    {
      isComplete: true,
      nodes: space.nodes,
      isMultiRes: true,
      isVisible,
      tourCapturedDate,
      tourLastUpdatedDate
    }
  )
}

export const createLidarScan = async (
  spaceId: string,
  nodesWithLinks: NodeWithLinksDTO[]
): Promise<Space> => {
  logDebug(location, 'Creating scan', { spaceId, nodesWithLinks })
  await saveSpaceNodesWithLinks(spaceId, nodesWithLinks)
  logDebug(location, 'Process Virtual Tour', { spaceId })
  const space = await processVirtualTour(spaceId)
  logDebug(location, 'Sending emails', { spaceId })
  sendScanEmails({
    communityId: space.community._id.toString(),
    street: space.building.address.street,
    unit: space.unit
  })

  return space ? applyDisplayOverrides(space) : null
}

export const sendScanEmails = async (params: SendScanEmail) => {
  try {
    const opsEmail = getEnvVariable('PEEK_OPERATIONS_EMAIL')
    const authUser = getAuthUser()
    const community = await findCommunityById(params.communityId)
    const query = {
      communityId: community._id,
      organizationId: community.organization._id,
      emailType: 'scanNotificationEmail',
      status: 'active'
    }
    const usersList = await findUserPoliciesByCommunity(query)
    const emails = [{ email: opsEmail }, ...usersList]
    emails.forEach((user) => {
      sendMail(user.email, KLAVIYO_EVENTS.NEW_SCAN_CREATED, {
        unit: params.unit,
        communityName: community.name,
        communityAddress: params.street,
        scannerName: authUser.name,
        userName: user.name ? user.name : ''
      })
    })
  } catch (error) {
    logWarn('services/space', 'Error sending email', error.message || '')
  }
}

export const findSpaceById = async (
  id: string,
  fields?: string,
  options?: QueryOptions
): Promise<Space | null> => {
  const space = await repo.findSpaceById(id, fields, options)
  return space ? applyDisplayOverrides(space) : null
}

export const findSpaces = async (
  queryObj: SpaceQuery,
  projection?: string,
  include?: string
): Promise<PaginatedResult<Space>> => {
  if (!queryObj['community._id']) {
    queryObj.limit = 100
  }

  const { template, ...query } = queryObj

  let modifiedProject = projection
  if (template) {
    modifiedProject = getSpaceTemplate(template as SpaceTemplate).projection
  }

  const [spaces, totalCount] = await Promise.all([
    repo.findSpaces(query, modifiedProject, include),
    repo.countSpaces(query)
  ])

  return { data: spaces.map(applyDisplayOverrides), totalCount }
}

export const findAllSpaces = async (
  query: SpaceQuery,
  projection?: string
): Promise<Space[]> => {
  const spaces = await repo.findAllSpaces(query, projection)
  return spaces.map(applyDisplayOverrides)
}

export const getSpaceByToken = async (token: string): Promise<Space | null> => {
  const spaceByTokenLegacy = await repo.getSpaceByTokenLegacy(token)
  if (spaceByTokenLegacy) return applyDisplayOverrides(spaceByTokenLegacy)
  const spaceByToken = await repo.getSpaceByToken(token)
  return applyDisplayOverrides(spaceByToken)
}

export const generateSpaceToken = async (id: string): Promise<string> => {
  const space = await repo.findSpaceByIdLean(id)
  if (!space) {
    logDebug('services/space', `Space with id ${id} not found.`, { id })
    throw new NotFoundError('Space not found.')
  }
  if (space.token) return space.token
  const token = nanoid(8)
  await addOrUpdateSpaceById(id, { token })
  return token
}

export const findSpacesByPublishApartmentsDotComCursor = async () => {
  const spaces = await repo.findSpacesByPublishApartmentsDotComCursor()
  return spaces.map(applyDisplayOverrides)
}

export const findSpacesByCommunityIdCursor = async (communityId: string) => {
  const spaces = await repo.findSpacesByCommunityIdCursor(communityId)
  return spaces.map(applyDisplayOverrides)
}

export const getFullAddress = (space: Partial<Space>): string => {
  const { address } = space?.building || {}

  const { street = '', city = '', state = '', postalCode = '' } = address || {}

  return !address ? '' : `${street} ${city} ${state} ${postalCode}`.trim()
}

export const findAllSpacesByCommunityIdPopulate = async (
  communityId: string
): Promise<Space[]> => {
  const spaces = await repo.findAllSpacesByCommunityIdPopulate(communityId)
  return spaces.map(applyDisplayOverrides)
}

export const findAllCompletedSpacesByCommunityIdPopulate = async (
  communityId: string,
  populate: string[]
): Promise<Space[]> => {
  const spaces = await repo.findAllCompletedSpacesByCommunityIdPopulate(
    communityId,
    populate
  )
  return spaces.map(applyDisplayOverrides)
}

export const findSpaceByToken = async (token: string) => {
  const space = await repo.findSpaceByToken(token)
  return applyDisplayOverrides(space)
}

export const getGmbPlaces = async (query: GmbPlacesQuery) => {
  const cachedLocations = await gmbLocationRepo.getGmbLocations(query)
  if (cachedLocations.length)
    return cachedLocations.map((l) =>
      pick(l, ['title', 'placeId', 'address', '_id'])
    )

  const gmbLocations = await fetchGmbPlaces(query)

  if (gmbLocations?.length) {
    const result = await gmbLocationRepo.saveGmbLocations(
      gmbLocations.map((location) => ({
        ...location,
        user: { _id: query.user }
      }))
    )

    return result.map((l) => pick(l, ['title', 'placeId', 'address', '_id']))
  }

  return []
}

export const unpublishSpaceFromGMB = async (
  payload: UnpublishSpaceToGMBPayload
) => {
  try {
    const { origin, spaceId, userId } = payload

    const token = await getGmbAccessToken({
      origin: origin,
      userId: userId
    })

    if (!token)
      throw new BadRequestError('Error connecting to google my business')

    const space = await SpaceModel.findById(spaceId).lean()
    if (!space) {
      throw new NotFoundError('Space not found')
    }

    const photoIds = space.nodes
      .map((node) => node.stagedPhoto?.googleId || node.photo.googleId)
      .filter(Boolean)

    await axios.post(
      `${process.env.STREET_VIEW_BASE_URL}/photos:batchDelete`,
      { photoIds },
      { headers: { Authorization: `Bearer ${token}` } }
    )

    const updatedNodes = space.nodes.map((node) => ({
      ...node,
      photo: { ...node.photo, googleId: null },
      ...(node.stagedPhoto && {
        stagedPhoto: { ...node.stagedPhoto, googleId: null }
      })
    }))

    await repo.addOrUpdateSpaceById(spaceId, {
      publishGmbStatus: 'unpublished',
      nodes: updatedNodes
    })

    return true
  } catch (error) {
    logError('services/space', 'Error unpublishing space from GMB', error)
    throw error
  }
}

export const publishSpaceToGMB = async (payload: PublishSpaceToGMBPayload) => {
  const { origin, placeId, publishStaged, spaceId, userId } = payload
  const token = await getGmbAccessToken({
    origin: origin,
    userId: userId
  })

  if (!token)
    throw new BadRequestError('Error connecting to google my business')

  await repo.addOrUpdateSpaceById(spaceId, {
    publishGmbStatus: 'processing',
    gmbUser: userId
  })

  const params = {
    spaceId,
    placeId: placeId,
    ...(publishStaged && { publishStaged })
  }

  await publish(params, getEnvVariable('PUBLISH_GMB_SPACE_QUEUE'))
}

export const findOneSpaceByQuery = async (
  query: any,
  projection?: string
): Promise<Space | null> => {
  const space = await repo.findOneSpaceByQuery(query, projection)
  return space ? applyDisplayOverrides(space) : null
}

export const bulkUpdate = async (spaces: Partial<Space>[]) => {
  const transactions = []
  for (const space of spaces) {
    if (space.community) {
      const community = await findCommunityById(space.community._id.toString())
      if (!community) throw new NotFoundError('Community not found.')
      space.community = community
    }
    transactions.push(repo.addOrUpdateSpaceById(space._id.toString(), space))
  }
  return Promise.all(transactions)
}

/**
 * @deprecated We will remove it a soon as we go to prod with the new ios app that generates the node links in-app
 */
export const generateNodeLinks = async (
  spaceId: string,
  dto: NodeLinkDto[]
) => {
  const space = await findSpaceById(spaceId)
  if (!space) throw new NotFoundError('Space not found.')

  const { nodes } = space

  const markers: Marker[] = nodes.map((node: Node) => {
    return {
      nodeId: node._id.toString(),
      position: {
        x: node.roomPlan.marker.position.x,
        y: node.roomPlan.marker.position.y,
        z: node.roomPlan.marker.position.z
      },
      links: [],
      name: node.label
    }
  })

  return createNodeLinksByMarkers(
    spaceId,
    findMarkersLinks(findDoorsInWall(dto), markers)
  )
}

export const deleteSpaceById = async (id: string): Promise<void> => {
  await repo.deleteSpaceById(id)
}

export const updateNotInSpacesAndRentPricesVisibility = async (
  spaceIds: string[],
  communityId: string,
  buildingId: string,
  visibility: boolean
): Promise<void> => {
  if (!communityId || !buildingId) {
    throw new BadRequestError('Community and building ids are required')
  }

  const notInSpaces = await repo.findSpacesByQuery(
    {
      _id: { $nin: spaceIds },
      'community._id': communityId,
      'building._id': buildingId,
      type: 'unit'
    },
    'rentPrices community building isVisible _id unit bedrooms bathrooms floorPlan availabilityStatus pricesMetadata isMarketable',
    { skipAclScope: true }
  )

  await Promise.all(
    notInSpaces.map(async (space: Space) => {
      try {
        let rentPrices = []
        if (space.rentPrices?.length > 0) {
          rentPrices = space.rentPrices.map((rentPrice) => {
            return {
              ...rentPrice,
              enabled: visibility
            }
          })
        }

        return addOrUpdateSpaceX(space._id.toString(), {
          isMarketable: visibility,
          isVisible: visibility,
          rentPrices,
          dxUpdate: buildSyncSuccess(space, rentPrices, visibility)
        })
      } catch (err) {
        await markSyncFail(space._id.toString(), err.message)
      }

      return Promise.resolve()
    })
  )
}

export async function updateMany(
  query: { [key: string]: any },
  space: Partial<Space>
): Promise<void> {
  await repo.updateMany(query, space)
}

export async function updateAllSpacesByCommunityExternalId() {
  const dxSettings = await findAllDxSettings(
    {
      service: DXSettingServices.FUNNEL,
      'dataExchange.syncPms': true,
      deletedAt: null
    },
    'communityId'
  )

  for (const dxSetting of dxSettings) {
    if (!dxSetting.communityId) {
      continue
    }

    const spaceIds = await findAllSpaces(
      {
        'community._id': dxSetting.communityId.toString(),
        type: SpaceType.Unit,
        isVisible: true
      },
      '_id'
    )
    logInfo(location, 'Spaces to update', {
      community: dxSetting.communityId.toString(),
      spaceIds
    })

    await updateMany({ _id: { $in: spaceIds } }, { isVisible: false })
    logInfo(location, 'Updated spaces', spaceIds)
  }
}

export const createSpaceAsync = async (
  eventBody: SpaceEventBody
): Promise<void> => {
  await publish(
    eventBody,
    getEnvVariable('CREATE_PMS_SPACE_TOPIC_QUEUE'),
    eventBody.community._id.toString(),
    nanoid()
  )
}

export const saveSpaceNodesWithLinks = async (
  spaceId: string,
  nodesWithLinks: NodeWithLinksDTO[]
): Promise<void> => {
  const nodes: Array<Node> = []

  for (const nodeWithLinks of nodesWithLinks) {
    const nodeId = new mongoose.Types.ObjectId()
    const node: Node = {
      _id: nodeId,
      label: nodeWithLinks.label,
      rotation: {
        pitch: +(nodeWithLinks?.rotation?.pitch || 0),
        roll: +(nodeWithLinks?.rotation?.roll || 0),
        yaw: +(nodeWithLinks?.rotation?.yaw || 0)
      },
      photo: {
        url: `${spaceId}/${nodeWithLinks.label}.jpg`
      },
      nodeLinks: []
    }

    nodes.push(node)
  }

  for (let i = 0; i <= nodes.length - 1; i++) {
    const nodeWithLinks = nodesWithLinks[i]
    const visibleLinks = nodeWithLinks.nodeLinks.filter((nl) => nl.isVisible)
    const nonVisibleLinks = nodeWithLinks.nodeLinks.filter(
      (nl) => !nl.isVisible
    )

    const linkMapper = (nl: any) => ({
      node: nodes.find((n) => n.label == nl.label)._id,
      label: nl.label,
      position: { x: +nl.position.x, y: +nl.position.y, z: 0 },
      type: NodeLinkType.Node,
      rotation: nl.rotation,
      autoLink: true,
      visibilityJustification: nl.visibilityJustification
    })

    nodes[i].nodeLinks = visibleLinks.map(linkMapper)
    nodes[i].nonVisibleNodeLinks = nonVisibleLinks.map(linkMapper)
  }

  await repo.updateOne(spaceId, { nodes, isMultiRes: false })
}

export const updateSpaceCommunityAndDisplayPrice = async (
  communityId: string,
  spaceCommunity: SpaceCommunity
) => {
  const spaces = await repo.findSpacesByQuery(
    {
      'community._id': communityId
    },
    'rentPrices pricesMetadata',
    { skipAclScope: true }
  )
  const updateData = spaces.map((space) => {
    const displayPrice = findDisplayPrice({
      displayPriceField: spaceCommunity.displayPriceField,
      rentPrices: space.rentPrices,
      pricesMetadata: space.pricesMetadata
    })

    return {
      _id: space._id,
      displayPrice,
      community: spaceCommunity
    }
  })
  return repo.bulkUpdateById(updateData)
}

export const updateSpaceCommunityById = async (
  communityId: string,
  spaceCommunity: SpaceCommunity
) => {
  return repo.updateSpaceCommunityById(communityId, spaceCommunity)
}

export const getPriceFieldsByCommunity = async (communityId: string) => {
  const spaces = await repo.findSpacesByQuery(
    {
      deletedAt: null,
      'community._id': communityId
    },
    'rentPrices pricesMetadata',
    { skipAclScope: true }
  )

  const priceFields = spaces.reduce((acc, space) => {
    const rentPrices = space.rentPrices
      .filter((p) => !!p.enabled)
      .map((p) => `rentPrices.${p.termInMonths}.price`)
    const metadataPrices = Object.keys(space.pricesMetadata || {})
    return [...acc, ...rentPrices, ...metadataPrices]
  }, [] as string[])

  return Array.from(new Set(priceFields))
}

export const getAutoScanFieldsByCommunity = async (
  communityId: string
): Promise<GetAutoScanFieldsByCommunity> => {
  const spaces = await repo.findSpacesByQuery(
    {
      'community._id': communityId
    },
    'availableDatesMetadata availableStatusesMetadata',
    { skipAclScope: true }
  )
  const dateFields: string[] = []
  const statusFields: { field: string; value: string }[] = []

  for (const space of spaces) {
    if (space.availableDatesMetadata) {
      dateFields.push(...Object.keys(space.availableDatesMetadata))
    }
    if (space.availableStatusesMetadata) {
      for (const statusField of Object.keys(space.availableStatusesMetadata)) {
        statusFields.push({
          field: statusField,
          value: String(space.availableStatusesMetadata[statusField])
        })
      }
    }
  }
  const uniqueStatusFields = Array.from(
    new Set(statusFields.map((s) => JSON.stringify(s)))
  ).map((s) => JSON.parse(s))

  const pastFields = []
  const formattedStatusFields = uniqueStatusFields.reduce((acc, status) => {
    if (!pastFields.includes(status.field)) {
      acc.push({
        field: status.field,
        values: uniqueStatusFields
          .filter((s) => s.field === status.field)
          .map((s) => s.value)
      })
      pastFields.push(status.field)
    }
    return acc
  }, [])

  return {
    dateFields: Array.from(new Set(dateFields)),
    statusFields: formattedStatusFields
  }
}

export function generatePopulate(include: string): PopulateOptions[] {
  const includeList = include?.split(',') ?? []

  const validKeys = Object.keys(populateIncludeMap())
  return includeList.reduce((acc, populateInclude) => {
    if (validKeys.includes(populateInclude)) {
      acc.push(populateIncludeMap()[populateInclude])
    }
    return acc
  }, [])
}

export const deduplicateSpaces = async (
  couple: CoupleSpaceIds
): Promise<CoupleResponse | undefined> => {
  logDebug(
    location,
    `Deduplicating spaces ${couple.syncedId} and ${couple.scannerId}`
  )
  const externalLinks = await findExternalLinks({
    objectId: couple.syncedId
  })

  if (!externalLinks?.length) {
    logInfo(
      location,
      `ABORT, no externalLink found for space ${couple.syncedId}`
    )
    return {
      success: false,
      message: `synced externalLink not found for space ${couple.syncedId}`
    }
  }

  const syncedSpace = await findSpaceById(couple.syncedId)
  const scannerSpace = await findSpaceById(couple.scannerId)

  try {
    if (!syncedSpace?.unit) {
      logInfo(location, `ABORT, space ${syncedSpace._id} has no unit`)
      return {
        success: false,
        message: `synced space ${syncedSpace._id} has no unit`
      }
    }

    if (!scannerSpace?.unit) {
      logInfo(location, `ABORT, scanner space ${couple.scannerId} not found`)
      return {
        success: false,
        message: `scanner space ${couple.scannerId} not found`
      }
    }

    logInfo(location, `Updating space ${syncedSpace._id}`)
    await deleteSpaceById(`${syncedSpace._id}`)

    logInfo(
      location,
      `Soft deleting scanner space ${scannerSpace.unit} to ${syncedSpace.unit}`
    )

    let building: Building

    if (couple.version === '1') {
      building = {
        address: syncedSpace.building.address,
        alternativeName: syncedSpace.building.alternativeName
      } as Building
    } else {
      building = {
        _id: syncedSpace.building._id,
        address: syncedSpace.building.address,
        alternativeName: syncedSpace.building.alternativeName
      } as Building
    }

    await updateSpaceById(couple.scannerId, {
      unit: syncedSpace.unit,
      building
    })

    logInfo(
      location,
      `Updating synced space ${syncedSpace.unit} to ${scannerSpace.unit}`
    )
    for (const externalLink of externalLinks) {
      await updateExternalLinkById(`${externalLink._id}`, {
        objectId: `${scannerSpace._id}`
      })
    }
  } catch (error) {
    logError(location, 'Error to update spaces', {
      message: error.message
    })

    if (couple.scannerId && couple.syncedId) {
      await updateSpaceById(couple.scannerId.toString(), {
        unit: scannerSpace?.unit ?? null
      })

      await updateSpaceById(couple.syncedId, {
        deletedAt: null
      })

      for (const externalLink of externalLinks) {
        await updateExternalLinkById(`${externalLink._id}`, {
          objectId: `${couple.syncedId}`
        })
      }

      logInfo(location, 'Rollback done')
      return undefined
    }
  }

  return {
    success: true,
    message: `space ${syncedSpace._id} updated`
  }
}

export const removeSpacePrices = async (
  space: SpaceRemovePrices,
  community?: (Community & { toObject?: any }) | undefined
): Promise<RemovePrice | undefined> => {
  if (!space) return
  if (!hasPriceFields(space)) return { result: space, community }

  if (space.toObject) {
    space = space.toObject({ getters: true })
  }

  let foundSpace: Space = { ...space }

  if (!community && !foundSpace.community?._id) {
    foundSpace = await findSpaceById(
      foundSpace?._id?.toString(),
      'community _id'
    )
  }

  if (!community || community.showPrices === undefined) {
    community = await findCommunityById(
      foundSpace.community?._id?.toString(),
      'showPrices'
    )
  }

  if (community?.toObject) community = community.toObject({ getters: true })

  if (community?.showPrices === false) {
    priceFields.forEach((field) => unset(space, field))
  }

  return { result: applyDisplayOverrides(space), community }
}

export const removeSpacesPrices = async (
  spaces: SpaceRemovePrices[],
  community?: Community | undefined
): Promise<Space[]> => {
  if (!spaces) return undefined
  if (!spaces.length) return []

  const communities: Record<string, Community> = {}
  const results: Space[] = []

  for (const space of spaces) {
    const communityInRecord =
      communities[
        community?._id.toString() || space?.community?._id?.toString()
      ]

    const data = await removeSpacePrices(space, communityInRecord ?? community)

    if (data.community) {
      communities[data.community._id.toString()] = data.community
    }

    results.push(data.result)
  }

  return results
}

const hasPriceFields = (space: Space): boolean =>
  priceFields.some((field) => get(space, field) !== undefined)

export const getAmenityCategoryByReference = async (
  unit: string
): Promise<Pick<Space, 'spaceCategory' | 'spaceFunction' | 'spaceDetail'>> => {
  const space = await repo.findOneByQuery(
    {
      unit,
      type: 'amenity',
      spaceCategory: { $exists: true, $ne: null },
      spaceFunction: { $exists: true, $ne: null },
      spaceDetail: { $exists: true, $ne: null }
    },
    'spaceCategory spaceFunction spaceDetail'
  )

  return space
}

export const getNodeCategoryByReference = async (
  label: string
): Promise<Pick<
  Space,
  'spaceCategory' | 'spaceFunction' | 'spaceDetail'
> | null> => {
  const space = await repo.findOneByQuery(
    {
      type: 'unit',
      nodes: {
        $elemMatch: {
          label,
          spaceCategory: { $exists: true, $ne: null },
          spaceFunction: { $exists: true, $ne: null },
          spaceDetail: { $exists: true, $ne: null }
        }
      }
    },
    { 'nodes.$': 1 }
  )

  return space?.nodes?.length > 0 ? space.nodes[0] : null
}

export const categorizeWithAI = async (
  roomName: string,
  tiers: any
): Promise<any> => {
  const apiKey = getEnvVariable('OPEN_AI_API_KEY')
  const apiUrl = getEnvVariable('CATEGORIZER_AI_ENDPOINT')

  const content = `
    You are a system that categorizes rooms based these tiers: ${JSON.stringify(
      tiers
    )}
  `

  try {
    const response = await axios.post(
      apiUrl,
      {
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content
          },
          {
            role: 'user',
            content: `return to me the tiers for this room: ${roomName} as json`
          }
        ],
        response_format: {
          type: 'json_object'
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`
        }
      }
    )

    const completion = response.data.choices[0].message.content

    const jsonMatch = completion.match(/\{[\s\S]*\}/)

    if (jsonMatch) {
      const parsedResponse = JSON.parse(jsonMatch[0])

      return parsedResponse
    }

    return {}
  } catch (error) {
    logError(location, 'Error to categorize room', {
      error
    })
    throw error
  }
}

export const categorizeAmenitiesAndNodes = async (
  id: string
): Promise<void> => {
  try {
    const space = await findSpaceById(
      id,
      'type spaceCategory spaceFunction spaceFunction nodes'
    )

    logDebug(location, 'Categorizing space', { space })

    if (space) {
      const isAmenity = space.type === 'amenity'
      const isUnit = space.type === 'unit'

      const isCategorized = [
        'spaceCategory',
        'spaceFunction',
        'spaceDetail'
      ].every((key) => space[key])

      if (isAmenity && !isCategorized) {
        publish(
          { spaceId: space._id.toString() },
          getEnvVariable('CATEGORIZER_AMENTITY_QUEUE')
        )
      }

      if (isUnit) {
        space.nodes?.forEach((node) => {
          const isNodeCategorized = [
            'spaceCategory',
            'spaceFunction',
            'spaceDetail'
          ].every((key) => node[key])

          if (!isNodeCategorized) {
            publish(
              { spaceId: space._id.toString(), nodeId: node._id.toString() },
              getEnvVariable('CATEGORIZER_NODE_QUEUE')
            )
          }
        })
      }
    }
  } catch (error) {
    logError(location, 'Error to categorize space', {
      error
    })
  }
}
