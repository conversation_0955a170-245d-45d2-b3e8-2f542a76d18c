import { Prospect } from '@modules/prospects/types/prospect'
import { Appointment } from '@modules/prospects/types/appointment'
import { DXSetting } from '@modules/communities/types/dxSetting'
import { makeInternalApiV2Request } from '@core/request/peekInternalApiClient'
import { AvailableDates, AvailableTimes } from '../manager'
import { TourType } from '@modules/anyoneHome/types/getTourAvailability'

export const sendAppointment = async (
  dxSetting: DXSetting,
  prospect: Prospect,
  appointment: Appointment
) => {
  const result = await makeInternalApiV2Request(
    `/dx-settings/${dxSetting.service}/send-appointment`,
    'POST',
    {
      dxSettingId: dxSetting._id.toString(),
      prospect: {
        prospectId: prospect._id.toString(),
        firstName: prospect.firstName,
        lastName: prospect.lastName,
        email: prospect.email,
        phone: prospect.phone,
        moveInDate: prospect.moveInDate
      },
      appointmentId: appointment._id.toString(),
      date: appointment.date,
      time: appointment.startTime
    }
  )
  return result
}

export const getAvailableDays = async (
  dxSetting: DXSetting,
  numberOfDays?: number,
  tourType?: TourType
): Promise<AvailableDates> => {
  if (!tourType) {
    tourType = TourType.GT
  }
  if (!numberOfDays) {
    numberOfDays = 7
  }
  const result = await makeInternalApiV2Request(
    `/dx-settings/${dxSetting.service}/available-days`,
    'GET',
    undefined,
    undefined,
    {
      numberOfDays: numberOfDays?.toString() || undefined,
      tourType: tourType,
      dxSettingId: dxSetting._id.toString()
    }
  )
  return result
}

export const getAvailableTimes = async (
  dxSetting: DXSetting,
  date: string,
  tourType: TourType = TourType.GT
): Promise<AvailableTimes> => {
  const result = await makeInternalApiV2Request(
    `/dx-settings/${dxSetting.service}/available-times`,
    'GET',
    undefined,
    undefined,
    {
      date,
      tourType,
      dxSettingId: dxSetting._id.toString()
    }
  )
  return result
}
