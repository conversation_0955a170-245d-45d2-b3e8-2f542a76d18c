import { Community } from '@modules/communities/types/community'
import { model, Schema } from 'mongoose'
import { Organization } from '../types/organization'
import { GoogleCredentials, User } from '../types/user'
import { mongoModelAclHook } from '@core/auth'

const PartialOrganizationSchema = new Schema<Partial<Organization>>({
  _id: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true }
})

const PartialCommunitySchema = new Schema<Partial<Community>>({
  _id: { type: Schema.Types.ObjectId, required: true },
  name: { type: String, required: true }
})

const PartialGoogleCredentials = new Schema<GoogleCredentials>({
  accessToken: { type: String, required: true },
  refreshToken: { type: String, required: true },
  scope: { type: String, required: true },
  tokenType: { type: String, required: true },
  expiryDate: { type: Number, required: true },
  accountName: { type: String },
  accountType: { type: String }
})

const userSchema = new Schema<User>(
  {
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String },
    phone: { type: String },
    roleId: { type: Schema.Types.ObjectId, required: true, ref: 'role' },
    status: {
      type: String,
      required: true,
      default: 'pending',
      validate: {
        validator: (value: string) =>
          ['active', 'inactive', 'pending', 'denied'].includes(value)
      }
    },
    deletedAt: { type: Date, default: null },
    organization: {
      type: PartialOrganizationSchema
    },
    organizations: {
      type: [PartialOrganizationSchema]
    },
    communities: {
      type: [PartialCommunitySchema]
    },
    googleCredentials: PartialGoogleCredentials,
    receiveEmailDigest: {
      type: Boolean,
      default: true
    },
    receiveROIReportEmail: {
      type: Boolean,
      default: true
    },
    receiveDailyEmailDigest: {
      type: Boolean,
      default: false
    },
    receiveProspectIntelligenceEmail: {
      type: Boolean,
      default: false
    },
    receiveSgtWeeklyReport: {
      type: Boolean,
      default: false
    },
    receiveAutoScanEmail: {
      type: Boolean
    },
    lastSignInAt: {
      type: Date,
      default: null
    },
    scanNotificationEmail: {
      type: Boolean,
      default: false
    },
    receiveScanScheduledConfirmation: {
      type: Boolean
    },
    receiveScanScheduledReminder: {
      type: Boolean
    },
    receiveScansSessionsReportEmail: {
      type: Boolean
    },
    receiveCanceledRequestsNotification: {
      type: Boolean
    },
    termsAndConditionsAcceptedAt: {
      type: Date
    }
  },
  {
    timestamps: true
  }
)

userSchema.virtual('role', {
  ref: 'role',
  localField: 'roleId',
  foreignField: '_id',
  justOne: true,
  options: { skipAclScope: true }
})

userSchema.virtual('policies', {
  ref: 'policy',
  localField: '_id',
  foreignField: 'userId',
  justOne: true,
  options: {
    skipAclScope: true,
    match: {
      resource: 'users'
    },
    projection: {
      deny: 1,
      allow: 1,
      _id: 0
    }
  }
})

userSchema.index({ 'organization.name': 1, deletedAt: 1 })
userSchema.index({ email: 1, deletedAt: 1 }, { unique: true })

userSchema.set('toObject', { virtuals: true })
userSchema.set('toJSON', { virtuals: true })

userSchema.pre('find', mongoModelAclHook)
userSchema.pre('findOne', mongoModelAclHook)
userSchema.pre('countDocuments', mongoModelAclHook)

export const UserModel = model<User>('user', userSchema)
