import Joi from 'joi'

export const FindUsersQueryRequest = Joi.object({
  offset: Joi.number().integer().min(0).default(0),
  limit: Joi.number().integer().min(10).max(100).default(10),
  search: Joi.string().default('').allow(''),
  include: Joi.array().items(Joi.string().valid('policies')).default([]),
  orderBy: Joi.string()
    .default('createdAt')
    .allow(
      'name',
      'email',
      'organization.name',
      'communities.name',
      'createdAt'
    ),
  order: Joi.string().default('desc').allow('asc', 'desc'),
  status: Joi.string().valid('pending', 'denied', 'active', 'inactive'),
  'organization._id': Joi.string(),
  roleId: Joi.string()
})

export const FindUserByIdParamRequest = Joi.object({
  id: Joi.string()
    .regex(/^[0-9a-fA-F]{24}$/)
    .required()
})

export const UpdateUserBodyRequest = Joi.object({
  roleId: Joi.string(),
  name: Joi.string(),
  email: Joi.string().email(),
  phone: Joi.string().optional().allow(''),
  fullAccess: Joi.boolean().default(false),
  organizations: Joi.array().items(Joi.string()),
  communities: Joi.array().items(Joi.string()),
  password: Joi.string()
    .optional()
    .pattern(
      new RegExp('^(?=.*\\d)(?=.*[!@#$%^&*])(?=.*[a-z])(?=.*[A-Z]).{8,}$')
    ),
  status: Joi.string().valid('pending', 'denied', 'active', 'inactive'),
  receiveEmailDigest: Joi.boolean().optional(),
  receiveDailyEmailDigest: Joi.boolean().optional(),
  receiveProspectIntelligenceEmail: Joi.boolean().optional(),
  scanNotificationEmail: Joi.boolean().optional(),
  receiveROIReportEmail: Joi.boolean().optional(),
  receiveSgtWeeklyReport: Joi.boolean().optional(),
  receiveAutoScanEmail: Joi.boolean().optional(),
  receiveScanScheduledConfirmation: Joi.boolean().optional(),
  receiveScanScheduledReminder: Joi.boolean().optional(),
  receiveScansSessionsReportEmail: Joi.boolean().optional(),
  receiveCanceledRequestsNotification: Joi.boolean().optional(),
  termsAndConditionsAcceptedAt: Joi.date().optional(),
  deny: Joi.array().items(Joi.string()).min(0).optional(),
  allow: Joi.array().items(Joi.string()).min(0).optional()
})

export const CreateUserBodyRequest = Joi.object({
  roleId: Joi.string().required(),
  name: Joi.string().required(),
  email: Joi.string().email().required(),
  phone: Joi.string().optional().allow(''),
  fullAccess: Joi.boolean().default(false),
  organizations: Joi.array().items(Joi.string()),
  communities: Joi.array().items(Joi.string()),
  receiveEmailDigest: Joi.boolean().optional(),
  receiveDailyEmailDigest: Joi.boolean().optional(),
  receiveProspectIntelligenceEmail: Joi.boolean().optional(),
  scanNotificationEmail: Joi.boolean().optional(),
  receiveROIReportEmail: Joi.boolean().optional(),
  receiveSgtWeeklyReport: Joi.boolean().optional(),
  receiveAutoScanEmail: Joi.boolean().optional(),
  receiveScanScheduledConfirmation: Joi.boolean().optional(),
  receiveScanScheduledReminder: Joi.boolean().optional(),
  receiveScansSessionsReportEmail: Joi.boolean().optional(),
  receiveCanceledRequest: Joi.boolean().optional(),
  receiveCanceledRequestsNotification: Joi.boolean().optional(),
  deny: Joi.array().items(Joi.string()).min(0).optional(),
  allow: Joi.array().items(Joi.string()).min(0).optional()
})

export const UserSchema = Joi.object({
  _id: Joi.string().min(24).max(24).example('5f5f5f5f5f5f5f5f5f5f5f5f'),
  status: Joi.string().valid('pending', 'denied', 'active', 'inactive'),
  deletedAt: Joi.date(),
  email: Joi.string().email(),
  name: Joi.string(),
  phone: Joi.string(),
  roleId: Joi.string(),
  organization: Joi.object({
    _id: Joi.string().min(24).max(24),
    name: Joi.string()
  }),
  communities: Joi.array().items(
    Joi.object({
      _id: Joi.string().min(24).max(24),
      name: Joi.string()
    })
  ),
  receiveEmailDigest: Joi.boolean().optional(),
  receiveDailyEmailDigest: Joi.boolean().optional(),
  receiveProspectIntelligenceEmail: Joi.boolean().optional(),
  scanNotificationEmail: Joi.boolean().optional(),
  receiveROIReportEmail: Joi.boolean().optional(),
  receiveSgtWeeklyReport: Joi.boolean().optional(),
  receiveAutoScanEmail: Joi.boolean().optional(),
  receiveScanScheduledConfirmation: Joi.boolean().optional(),
  receiveScanScheduledReminder: Joi.boolean().optional(),
  receiveScansSessionsReportEmail: Joi.boolean().optional(),
  receiveCanceledRequest: Joi.boolean().optional(),
  termsAndConditionsAcceptedAt: Joi.date().optional()
})
