import { Community } from '@modules/communities/types/community'
import { FilterQuery, ObjectId, Schema } from 'mongoose'
import { Organization } from './organization'
import { Role } from './role'
import { Id } from '@modules/communities/types/id'
import { UserModel } from '@modules/users/models/user'

export interface GoogleCredentials {
  accessToken: string
  refreshToken: string
  scope: string
  tokenType: string
  expiryDate: number
  accountName: string
  accountType: string
}

export type UserTag = {
  scanNotificationEmail?: boolean
  receiveScanScheduledConfirmation?: boolean
  receiveScanScheduledReminder?: boolean
  receiveScansSessionsReportEmail?: boolean
  receiveCanceledRequestsNotification?: boolean
}

export interface User {
  _id: string | Id
  status: 'pending' | 'denied' | 'active' | 'inactive'
  deletedAt: Date | null
  email: string
  name: string
  password?: string
  phone?: string
  role?: Role
  roleId: string | Schema.Types.ObjectId
  /** @deprecated it need to be removed after we migration to organizations */
  organization?: Pick<Organization, 'name' | '_id'>
  organizations?: Array<Pick<Organization, 'name' | '_id'>>
  communities?: Array<Pick<Community, 'name' | '_id'>>
  googleCredentials?: GoogleCredentials
  receiveEmailDigest?: boolean
  receiveDailyEmailDigest?: boolean
  receiveROIReportEmail?: boolean
  receiveProspectIntelligenceEmail?: boolean
  receiveSgtWeeklyReport?: boolean
  receiveAutoScanEmail?: boolean
  lastSignInAt?: Date
  scanNotificationEmail?: boolean
  receiveScanScheduledConfirmation?: boolean
  receiveScanScheduledReminder?: boolean
  receiveScansSessionsReportEmail?: boolean
  receiveCanceledRequestsNotification?: boolean
  termsAndConditionsAcceptedAt?: Date
  deny?: string[]
  allow?: string[]
}

export type UpdateUserDTO = Partial<
  Omit<User, 'organization' | 'communities'>
> & {
  organizations?: string[]
  communities?: string[]
}

export type UserQuery = FilterQuery<typeof UserModel> & {
  offset?: number
  limit?: number
  search?: string
  orderBy?:
    | 'createdAt'
    | 'name'
    | 'email'
    | 'organization.name'
    | 'communities.name'
  order?: 'asc' | 'desc'
  roleId?: string | { $in: string[] } | ObjectId
}

export type UserParams = {
  id: string
}

export type AuthUser = Partial<User> & {
  allow: string[]
  deny: string[]
  roleAlias?: string
  proxyUser?: AuthUser
}

export const ADMIN_ORG_DOMAINS = ['peek.us', 'ext.peek.us']
