import { SQSEvent } from 'aws-lambda'
import { findDXSettingById } from '@modules/communities/services/dxSetting'
import { findProspectById } from '@modules/prospects/services/prospect'
import {
  fetchProspectByEmail,
  sendProspect
} from '@modules/yardi/services/yardi'
import { logError, logInfo, logWarn } from '@core/log'
import { updateOneProspectSgtSync } from '@modules/prospects/services/prospectSgtSync'
import { ProspectSgtSync } from '@modules/prospects/types/prospectSgtSync'
import { connectDb } from '@core/db'

const location = 'update-yardi-prospect'

export async function handler(event: SQSEvent) {
  await connectDb()

  for (const record of event.Records) {
    logInfo(location, 'Received yardi prospect event', record)
    const body = JSON.parse(record.body)

    const [dxSetting, prospect] = await Promise.all([
      findDXSettingById(body.dxSettingId),
      findProspectById(body.prospectId)
    ])

    logInfo(location, 'Updating prospect comment yardi prospect', { prospect })
    try {
      const yardiProspect = await fetchProspectByEmail(
        prospect.email,
        dxSetting
      )

      if (!yardiProspect) {
        logWarn(location, 'Prospect not found in Yardi', { body })
        return
      }

      await sendProspect(prospect, dxSetting, 'PEEK_NO_SGT_BOOKED', false)
      await updateOneProspectSgtSync({ _id: body.syncId }, {
        reviewed: true
      } as ProspectSgtSync)
    } catch (err) {
      logError(location, 'Error updating yardi prospect', {
        body,
        error: err.message
      })
      throw err
    }
    logInfo(location, 'Updated yardi prospect', { prospect })
  }
}
