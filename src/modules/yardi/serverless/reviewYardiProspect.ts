import { logInfo } from '@core/log'
import { connectDb } from '@core/db'
import { subHours, subMinutes } from 'date-fns'
import { DXSettingServices } from '@modules/communities/types/dxSetting'
import { publish } from '@core/sqs'
import { getEnvVariable } from '@core/util'
import { findManyProspectSgtSync } from '@modules/prospects/services/prospectSgtSync'
import { nanoid } from 'nanoid'

const location = 'yardi-review-prospects'

export async function handler() {
  await connectDb()

  const currentDate = new Date()
  const lastFifteenMinutes = subMinutes(currentDate, 15)
  const sixHoursAgo = subHours(currentDate, 6)
  logInfo(location, 'Current date', { currentDate, lastFifteenMinutes })

  const results = await findManyProspectSgtSync({
    createdAt: { $gte: sixHoursAgo, $lte: lastFifteenMinutes },
    service: DXSettingServices.YARDI,
    reviewed: false,
    walkabout: null
  })
  logInfo(location, 'Prospects to sync found', { prospects: results })

  for (const result of results) {
    await publish(
      {
        prospectId: result.prospect.toString(),
        dxSettingId: result.dxSetting.toString(),
        syncId: result._id.toString()
      },
      getEnvVariable('YARDI_UPDATE_COMMENTS_QUEUE'),
      result.prospect.toString(),
      nanoid()
    )
  }

  logInfo(location, 'Prospects published', { prospects: results })
}
