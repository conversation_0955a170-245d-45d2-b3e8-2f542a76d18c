import {
  addConnectedAccount,
  createConnectWebView
} from '@modules/seam/services/createConnectWebView'
import { Router, Request, Response } from 'express'
import { addSeamDevice } from '@modules/seam/services/device'
import validateBody from '@api/middlewares/validateBody'
import authenticate from '@api/middlewares/auth'
import authenticateWebhook from '@api/middlewares/webhookAuth'
import { SeamWebhookRequest } from '@modules/seam/schemas/SeamWebhookRequest'
import { SeamConnectRequest } from '@modules/seam/schemas/SeamConnectRequest'
import { logDebug } from '@core/log'

const router = Router()
const activeSseConnections = new Map()
interface EventData {
  communityId: string
  event: string
}

const location = 'sgt/routes/seam'

export const sendEventToClients = (eventData: EventData) => {
  const client = activeSseConnections.get(eventData.communityId)

  try {
    client.write(`data: ${JSON.stringify(eventData)}\n\n`)
  } catch {
    //do nothing
  }
}

router.post('/webhook', [
  validateBody(SeamWebhookRequest),
  authenticateWebhook('SEAM_WEBHOOK_API_KEY'),
  async (req: Request, res: Response) => {
    const event = req.body

    logDebug(location, 'Seam webhook received', { event })

    if (event.event_type == 'device.added') {
      const connectedAccountId = event.connected_account_id
      const deviceId = event.data.device_id

      const accessDevice = await addSeamDevice(deviceId, connectedAccountId)

      sendEventToClients({
        communityId: accessDevice.community._id.toString(),
        event: 'device_added'
      })
    }

    if (event.event_type == 'connect_webview.login_succeeded') {
      const connectWebviewId = event.connect_webview_id
      const connectedAccountId = event.connected_account_id
      await addConnectedAccount(connectWebviewId, connectedAccountId)
    }

    res.status(200).send('Webhook received')
  }
])

router.post('/connect', [
  validateBody(SeamConnectRequest),
  authenticate(),
  async (req: Request, res: Response) => {
    logDebug(location, 'Seam connect request received', { req: req.body })
    const communityId = req.body.communityId

    const { connectWebviewId, url } = await createConnectWebView({
      communityId
    })

    res.status(200).json({ connectWebviewId, url })
  }
])

router.get(
  '/watch-events/:communityId',
  async (req: Request, res: Response) => {
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')
    res.setHeader('Access-Control-Allow-Origin', '*')

    res.write(
      `data: ${JSON.stringify({
        communityId: req.params.communityId,
        event: 'connected'
      })}\n\n`
    )

    const communityId = req.params.communityId
    activeSseConnections.set(communityId, res)
  }
)

export const seamRouter = Router().use('/seam', router)
