import { connectDb } from '@core/db'
import { logError, logInfo } from '@core/log'
import { loadRentCafeV2Secrets } from '@modules/rentCafeV2/services/'
import { syncVirtualTours } from '@modules/rentCafeV2/services/syncVirtualTours'

const location = 'sync-rent-cafe-v2-virtual-tours'

export const handler = async () => {
  logInfo(location, 'Starting sync', {})
  await connectDb()
  await loadRentCafeV2Secrets()

  try {
    await syncVirtualTours()
  } catch (error) {
    logError(location, 'Error syncing', {
      error: error.message
    })
  }

  logInfo(location, 'Finished sync', {})

  return {
    statusCode: 200,
    message: 'OK'
  }
}
