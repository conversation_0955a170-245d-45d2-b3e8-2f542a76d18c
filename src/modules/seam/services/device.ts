import { DXSettingServices } from '@modules/communities/types/dxSetting'
import { findOneDXSetting } from '@modules/communities/services/dxSetting'
import { CreateAccessDeviceInput } from '@modules/sgt/schemas/accessDevices'
import { createAccessDevice } from '@modules/sgt/services/accessDevice'
import {
  AccessDeviceManufacturer,
  AccessDeviceType
} from '@modules/sgt/types/accessDevice'
import { Seam } from 'seam'
import { logError } from '@core/log'
import { getEnvVariable } from '@core/util'

const location = 'seam-service'

let seam: Seam

const initSeam = () => {
  if (!seam) {
    seam = new Seam({
      apiKey: getEnvVariable('SEAM_API_KEY')
    })
  }
}

export const getDevice = async ({ deviceId }): Promise<any> => {
  initSeam()

  const devices = await seam.devices.list({
    device_ids: [deviceId]
  })

  return devices[0]
}

export const addSeamDevice = async (
  deviceId: string,
  connectedAccountId: string
) => {
  initSeam()

  const seamDevice = await getDevice({
    deviceId
  })

  const dxSetting = await findOneDXSetting({
    service: DXSettingServices.SEAM,
    'seam.connectedAccountIds': connectedAccountId
  })

  if (!dxSetting) {
    throw new Error('DXSETTING NOT FOUND')
  }

  let type = undefined
  if (seamDevice.capabilities_supported.includes('access_code')) {
    type = AccessDeviceType.Pinpad
  } else {
    logError(
      location,
      `Device type not supported: ${JSON.stringify(seamDevice, null, 2)}`
    )
    throw new Error('Device type not supported')
  }

  const manufacturer = getManufacturer(
    seamDevice.properties.model.manufacturer_display_name
  )

  const accessDeviceData: CreateAccessDeviceInput = {
    name: seamDevice.display_name,
    identifier: deviceId,
    isSeamManaged: true,
    metadata: seamDevice,
    imageUrl: seamDevice.properties.image_url,
    model: seamDevice.properties.model.display_name,
    manufacturer,
    type,
    communityId: dxSetting.communityId.toString()
  }

  const accessDevice = await createAccessDevice(accessDeviceData, true)

  return accessDevice
}

const getManufacturer = (manufacturer: string): AccessDeviceManufacturer => {
  switch (manufacturer) {
    case 'Yale':
      return AccessDeviceManufacturer.Yale
    case 'August': //TODO check why is august
      return AccessDeviceManufacturer.Yale
    case 'Latch':
      return AccessDeviceManufacturer.Latch
    case 'Igloohome':
      return AccessDeviceManufacturer.Igloohome
    case 'Salto':
      return AccessDeviceManufacturer.Salto
    default:
      throw new Error(`Manufacturer not supported: ${manufacturer}`)
  }
}
