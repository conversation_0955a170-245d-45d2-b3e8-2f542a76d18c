//createConnectWebView

import { logError } from '@core/log'
import { getEnvVariable } from '@core/util'
import { DXSettingModel } from '@modules/communities/models/dxSetting'
import { findOneDXSetting } from '@modules/communities/repositories/dxSetting'
import {
  addOrUpdateExternalLinkBy,
  findExternalLinkBy
} from '@modules/communities/repositories/externalLink'
import { DXSettingServices } from '@modules/communities/types/dxSetting'
import {
  ExternalLinkCollection,
  ExternalLinkName,
  ExternalLinkService
} from '@modules/communities/types/externalLink'
import { Seam } from 'seam'

const location = 'seam/services/createConnectWebView'

let seam: Seam

const initSeam = () => {
  if (!seam) {
    seam = new Seam({
      apiKey: getEnvVariable('SEAM_API_KEY')
    })
  }
}

export const createConnectWebView = async ({ communityId }): Promise<any> => {
  initSeam()

  const parrotsUrl = getEnvVariable('ADMIN_WEB_URL')
  const createdConnectWebview = await seam.connectWebviews.create({
    custom_redirect_url: `${parrotsUrl}/sgt/${communityId}/access-devices`,
    custom_redirect_failure_url: `${parrotsUrl}/sgt/${communityId}/access-devices`,
    accepted_providers: ['yale', 'salto', 'igloohome', 'latch', 'schlage'],
    wait_for_device_creation: true,
    custom_metadata: {
      communityId
    }
  })

  const connectWebviewId = createdConnectWebview.connect_webview_id
  const url = createdConnectWebview.url

  await addOrUpdateExternalLinkBy(
    {
      service: ExternalLinkService.Seam,
      collectionPath: ExternalLinkCollection.Communities,
      externalName: ExternalLinkName.SeamConnectWebviewId,
      objectId: communityId,
      communityId
    },
    {
      service: ExternalLinkService.Seam,
      collectionPath: ExternalLinkCollection.Communities,
      externalName: ExternalLinkName.SeamConnectWebviewId,
      objectId: communityId,
      communityId,
      externalId: connectWebviewId
    }
  )

  return {
    connectWebviewId,
    url
  }
}

export const addConnectedAccount = async (
  connectWebviewId: string,
  connectedAccountId: string
) => {
  const externalLink = await findExternalLinkBy({
    service: ExternalLinkService.Seam,
    collectionPath: ExternalLinkCollection.Communities,
    externalName: ExternalLinkName.SeamConnectWebviewId,
    externalId: connectWebviewId
  })

  const communityId = externalLink?.communityId
  const dxSetting = await findOneDXSetting({
    communityId,
    service: DXSettingServices.SEAM
  })
  if (!dxSetting) {
    logError(location, 'ERROR: DXSETTING NOT FOUND')
    return
  }

  if (!dxSetting.seam.connectedAccountIds.includes(connectedAccountId)) {
    await DXSettingModel.updateOne(
      { _id: dxSetting._id },
      { $push: { 'seam.connectedAccountIds': connectedAccountId } }
    )
  }
}
