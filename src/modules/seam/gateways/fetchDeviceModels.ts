import { getEnvVariable } from '@core/util'
import { Seam } from 'seam'

let seam: Seam

const initSeam = () => {
  if (!seam) {
    seam = new Seam({
      apiKey: getEnvVariable('SEAM_API_KEY')
    })
  }
}

export const fetchDeviceModels = async () => {
  initSeam()

  const allDevices = await seam.devices.list()
  const devices = allDevices.map((device) => {
    return {
      device_id: device.device_id,
      type: device.device_type,
      capabilities: device.capabilities_supported,
      model: device.properties.model.display_name
    }
  })
  return devices
}
