import { getEnvVariable, sleep } from '@core/util'
import { DevicesListParams, Seam } from 'seam'

let seam: Seam

const initSeam = () => {
  if (!seam) {
    seam = new Seam({
      apiKey: getEnvVariable('SEAM_API_KEY')
    })
  }
}

export const generateSeamAccessCode = async (
  deviceId: string,
  startsAt: string,
  endsAt: string
) => {
  initSeam()

  const params: DevicesListParams = {
    device_ids: [deviceId]
  }

  const devices = await seam.devices.list(params)
  const device = devices[0]

  const accessCode = await seam.accessCodes.create({
    device_id: device.device_id,
    starts_at: startsAt,
    ends_at: endsAt,
    name: 'Test Access Code',
    is_offline_access_code: device.can_program_offline_access_codes
  })

  let code = accessCode.code
  while (!code) {
    await sleep(1000)
    const accessCodeResponse = await seam.accessCodes.get({
      device_id: deviceId,
      access_code_id: accessCode.access_code_id
    })

    code = accessCodeResponse.code
  }
  return code
}
