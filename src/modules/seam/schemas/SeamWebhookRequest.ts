import Joi from 'joi'

export const SeamWebhookRequest = Joi.object({
  access_code_id: Joi.string().optional(),
  connect_webview_id: Joi.string().optional(),
  connected_account_id: Joi.string().optional(),
  created_at: Joi.date().optional(),
  data: Joi.object({
    access_code_id: Joi.string().optional(),
    connect_webview_id: Joi.string().optional(),
    connected_account_id: Joi.string().optional(),
    deprecation_warning: Joi.string().optional(),
    device_id: Joi.string().optional(),
    workspace_id: Joi.string().optional()
  }).optional(),
  device_id: Joi.string().optional(),
  event_description: Joi.string().optional(),
  event_id: Joi.string().optional(),
  event_type: Joi.string().optional(),
  occurred_at: Joi.date().optional(),
  workspace_id: Joi.string().optional()
})
